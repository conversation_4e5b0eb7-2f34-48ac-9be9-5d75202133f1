["tests/integration/test_full_workflow.py::TestFullWorkflowIntegration::test_full_workflow_with_mocked_data", "tests/unit/test_base_agent.py::TestAgentWithLangGraphState::test_state_modification", "tests/unit/test_base_agent.py::TestAgentWithLangGraphState::test_state_preservation", "tests/unit/test_base_agent.py::TestBaseAgent::test_agent_initialization", "tests/unit/test_base_agent.py::TestBaseAgent::test_agent_initialization_with_invalid_model", "tests/unit/test_base_agent.py::TestBaseAgent::test_agent_initialization_without_model", "tests/unit/test_base_agent.py::TestBaseAgent::test_default_prompt_template", "tests/unit/test_base_agent.py::TestBaseAgent::test_history_operations", "tests/unit/test_base_agent.py::TestBaseAgent::test_load_nonexistent_memory", "tests/unit/test_base_agent.py::TestBaseAgent::test_log_interaction", "tests/unit/test_base_agent.py::TestBaseAgent::test_memory_operations", "tests/unit/test_base_agent.py::TestBaseAgent::test_process_method_with_langgraph_state", "tests/unit/test_base_agent.py::TestBaseAgent::test_prompt_history", "tests/unit/test_base_agent.py::TestBaseAgent::test_reflect_method", "tests/unit/test_base_agent.py::TestBaseAgent::test_save_and_load_memory", "tests/unit/test_base_agent.py::TestBaseAgent::test_serializable_data_handling", "tests/unit/test_langgraph_nodes.py::TestLangGraphNodes::test_check_data_availability_node", "tests/unit/test_langgraph_nodes.py::TestLangGraphNodes::test_check_data_availability_node_no_data", "tests/unit/test_langgraph_nodes.py::TestLangGraphNodes::test_collect_analysis_node", "tests/unit/test_langgraph_nodes.py::TestLangGraphNodes::test_faa_node", "tests/unit/test_langgraph_nodes.py::TestLangGraphNodes::test_finalize_results_node", "tests/unit/test_langgraph_nodes.py::TestLangGraphNodes::test_initialize_data_node", "tests/unit/test_langgraph_nodes.py::TestLangGraphNodes::test_naa_node", "tests/unit/test_langgraph_nodes.py::TestLangGraphNodes::test_node_error_handling", "tests/unit/test_langgraph_nodes.py::TestLangGraphNodes::test_outlook_agents_nodes", "tests/unit/test_langgraph_nodes.py::TestLangGraphNodes::test_route_base_analysts", "tests/unit/test_langgraph_nodes.py::TestLangGraphNodes::test_route_to_optimization", "tests/unit/test_langgraph_nodes.py::TestLangGraphNodes::test_state_consistency_across_nodes", "tests/unit/test_langgraph_nodes.py::TestLangGraphNodes::test_taa_node", "tests/unit/test_langgraph_nodes.py::TestLangGraphNodes::test_tra_node", "tests/unit/test_news_analyst_agent.py::TestNewsAnalystAgent::test_agent_initialization", "tests/unit/test_news_analyst_agent.py::TestNewsAnalystAgent::test_format_news_data", "tests/unit/test_news_analyst_agent.py::TestNewsAnalystAgent::test_format_news_data_limit", "tests/unit/test_news_analyst_agent.py::TestNewsAnalystAgent::test_format_news_data_with_missing_fields", "tests/unit/test_news_analyst_agent.py::TestNewsAnalystAgent::test_model_interface_error_handling", "tests/unit/test_news_analyst_agent.py::TestNewsAnalystAgent::test_output_format_validation", "tests/unit/test_news_analyst_agent.py::TestNewsAnalystAgent::test_process_with_news_data", "tests/unit/test_news_analyst_agent.py::TestNewsAnalystAgent::test_process_with_none_news_data", "tests/unit/test_news_analyst_agent.py::TestNewsAnalystAgent::test_process_without_news_data", "tests/unit/test_news_analyst_agent.py::TestNewsAnalystAgent::test_prompt_template_formatting", "tests/unit/test_news_analyst_agent.py::TestNewsAnalystAgent::test_reflect_method", "tests/unit/test_news_analyst_agent.py::TestNewsAnalystAgent::test_state_preservation"]