#!/usr/bin/env python3
"""
Integration tests for the full LangGraph workflow

Tests the complete multi-agent trading system workflow from
data initialization through final trading decisions.
"""

import unittest
import os
import sys
import tempfile
import shutil
from unittest.mock import Mock, patch
import pandas as pd
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from langgraph_coordinator import LangGraphCoordinator
from utils.langgraph_state import LangGraphState
from run_multi_agent import prepare_ticker_specific_config


class TestFullWorkflowIntegration(unittest.TestCase):
    """Integration tests for the complete workflow"""
    
    def setUp(self):
        """Set up test fixtures"""
        # Create temporary directory
        self.temp_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.temp_dir)
        
        # Create test configuration
        self.base_config = {
            "timestamp": datetime.now().strftime("%Y%m%d_%H%M%S"),
            "model_config": {"model_name": "test_model"},
            "optimization_config": {"enable": False},
            "reflection_config": {"enable": False},
            "myerson_config": {"enable": False},
            "verbose": False,
            "env_config_base": {
                "starting_cash": 100000,
                "start_date": "2025-01-01",
                "end_date": "2025-01-31"
            }
        }
        
        # Create mock data
        self.mock_price_data = pd.DataFrame({
            "Date": pd.date_range("2025-01-01", "2025-01-31"),
            "Open": [150.0] * 31,
            "High": [155.0] * 31,
            "Low": [145.0] * 31,
            "Close": [152.0] * 31,
            "Volume": [1000000] * 31
        })
        
        self.mock_news_data = [
            {
                "title": "Apple Reports Strong Q4 Earnings",
                "time_published": "2025-01-15T10:30:00",
                "summary": "Apple exceeded expectations with strong iPhone sales",
                "source": "Reuters"
            }
        ]
        
        self.mock_fundamental_data = {
            "AAPL": {
                "pe_ratio": 25.0,
                "market_cap": 3000000000000,
                "revenue": 365000000000,
                "eps": 6.05
            }
        }
    
    def tearDown(self):
        """Clean up test fixtures"""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.temp_dir)
    
    def test_full_workflow_with_mocked_data(self):
        """Test complete workflow with mocked data and agents"""
        # Prepare configuration
        ticker_config = prepare_ticker_specific_config(
            self.base_config, "AAPL", "test_run_123"
        )
        
        # Mock the trading environment
        with patch('langgraph_coordinator.StockTradingEnv') as mock_env_class:
            mock_env = Mock()
            mock_env.reset.return_value = {
                "date": "2025-01-15",
                "cash": 100000,
                "positions": {},
                "price_history": {"AAPL": self.mock_price_data},
                "news_history": self.mock_news_data,
                "fundamental_data": self.mock_fundamental_data
            }
            mock_env.step.return_value = (
                {
                    "date": "2025-01-16",
                    "cash": 95000,
                    "positions": {"AAPL": 10},
                    "price_history": {"AAPL": self.mock_price_data},
                    "news_history": self.mock_news_data,
                    "fundamental_data": self.mock_fundamental_data
                },
                500.0,  # reward
                False,  # done
                {}      # info
            )
            mock_env_class.return_value = mock_env
            
            # Mock agents
            with patch.object(LangGraphCoordinator, '_create_agents') as mock_create_agents:
                mock_agents = self._create_comprehensive_mock_agents()
                mock_create_agents.return_value = mock_agents
                
                # Create coordinator
                coordinator = LangGraphCoordinator(ticker_config)
                
                # Run the workflow
                result = coordinator.run(num_days=5)
                
                # Verify result structure
                self.assertIsInstance(result, dict)
                self.assertIn("final_state", result)
                self.assertIn("execution_time", result)
                
                # Verify final state
                final_state = result["final_state"]
                # The final state might be serialized to dict format
                if isinstance(final_state, dict):
                    self.assertEqual(final_state["ticker"], "AAPL")
                    self.assertEqual(final_state["run_id"], "test_run_123")
                else:
                    self.assertIsInstance(final_state, LangGraphState)
                    self.assertEqual(final_state.ticker, "AAPL")
                    self.assertEqual(final_state.run_id, "test_run_123")
    
    def _create_comprehensive_mock_agents(self):
        """Create comprehensive mock agents for testing"""
        mock_agents = {}
        
        # News Analyst Agent
        naa = Mock()
        naa.agent_id = "NAA"
        naa.process = Mock(side_effect=self._mock_naa_process)
        naa.reflect = Mock(return_value={"reflection": "NAA reflection"})
        mock_agents["NAA"] = naa
        
        # Technical Analyst Agent
        taa = Mock()
        taa.agent_id = "TAA"
        taa.process = Mock(side_effect=self._mock_taa_process)
        taa.reflect = Mock(return_value={"reflection": "TAA reflection"})
        mock_agents["TAA"] = taa
        
        # Fundamental Analyst Agent
        faa = Mock()
        faa.agent_id = "FAA"
        faa.process = Mock(side_effect=self._mock_faa_process)
        faa.reflect = Mock(return_value={"reflection": "FAA reflection"})
        mock_agents["FAA"] = faa
        
        # Outlook Agents
        for agent_id in ["BOA", "BeOA", "NOA"]:
            agent = Mock()
            agent.agent_id = agent_id
            agent.process = Mock(side_effect=self._mock_outlook_process)
            agent.reflect = Mock(return_value={"reflection": f"{agent_id} reflection"})
            mock_agents[agent_id] = agent
        
        # Trader Agent
        tra = Mock()
        tra.agent_id = "TRA"
        tra.process = Mock(side_effect=self._mock_tra_process)
        tra.reflect = Mock(return_value={"reflection": "TRA reflection"})
        mock_agents["TRA"] = tra
        
        return mock_agents
    
    def _mock_naa_process(self, state):
        """Mock NAA processing"""
        state.step_count += 1
        state.update_agent_output("NAA", {
            "key_events": ["Mock news event"],
            "market_sentiment_score": 7,
            "ticker_specific_impact": "Positive impact",
            "key_investment_factors": ["Strong earnings"],
            "confidence_rating": 4
        })
        return state
    
    def _mock_taa_process(self, state):
        """Mock TAA processing"""
        state.step_count += 1
        state.update_agent_output("TAA", {
            "trend_analysis": "Bullish trend",
            "support_resistance": {"support": 145.0, "resistance": 160.0},
            "technical_indicators": {"RSI": 65, "MACD": "bullish"},
            "confidence_rating": 4
        })
        return state
    
    def _mock_faa_process(self, state):
        """Mock FAA processing"""
        state.step_count += 1
        state.update_agent_output("FAA", {
            "valuation_analysis": "Fair value",
            "financial_health": "Strong",
            "growth_prospects": "Positive",
            "confidence_rating": 4
        })
        return state
    
    def _mock_outlook_process(self, state):
        """Mock outlook agent processing"""
        state.step_count += 1
        return state
    
    def _mock_tra_process(self, state):
        """Mock TRA processing"""
        state.step_count += 1
        state.update_agent_output("TRA", {
            "action": "BUY",
            "quantity": 10,
            "reasoning": "Strong fundamentals and positive sentiment",
            "confidence": 0.8
        })
        return state


if __name__ == '__main__':
    unittest.main() 