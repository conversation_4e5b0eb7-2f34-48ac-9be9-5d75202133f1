#!/usr/bin/env python3
"""
Unit tests for LangGraph node functions

Tests the individual node functions in the LangGraphCoordinator
to ensure they properly handle LangGraphState and execute agent logic.
"""

import unittest
import os
import sys
import tempfile
import shutil
from unittest.mock import Mock, patch, MagicMock
import pandas as pd

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from langgraph_coordinator import LangGraphCoordinator
from utils.langgraph_state import LangGraphState
from stock_trading_env import StockTradingEnv


class TestLangGraphNodes(unittest.TestCase):
    """Test cases for LangGraph node functions"""
    
    def setUp(self):
        """Set up test fixtures"""
        # Create mock configuration
        self.config = {
            "run_id": "test_run",
            "env_config": {
                "stocks": ["AAPL"],
                "starting_cash": 100000,
                "start_date": "2025-01-01",
                "end_date": "2025-01-31"
            },
            "enable_optimization": False,
            "enable_myerson_calculation": False,
            "model_name": "test_model"
        }
        
        # Create temporary directory
        self.temp_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.temp_dir)
        
        # Mock the trading environment
        with patch('langgraph_coordinator.StockTradingEnv') as mock_env_class:
            mock_env = Mock()
            mock_env.reset.return_value = {
                "date": "2025-01-15",
                "cash": 100000,
                "positions": {},
                "price_history": {"AAPL": pd.DataFrame({"Close": [150.0, 151.0]})},
                "news_history": [{"title": "Test news", "summary": "Test"}],
                "fundamental_data": {"AAPL": {"pe_ratio": 25.0}}
            }
            mock_env_class.return_value = mock_env
            
            # Create coordinator with mocked agents
            with patch.object(LangGraphCoordinator, '_create_agents') as mock_create_agents:
                mock_agents = self._create_mock_agents()
                mock_create_agents.return_value = mock_agents
                
                self.coordinator = LangGraphCoordinator(self.config)
                self.coordinator.agents = mock_agents
    
    def tearDown(self):
        """Clean up test fixtures"""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.temp_dir)
    
    def _create_mock_agents(self):
        """Create mock agents for testing"""
        mock_agents = {}
        
        agent_ids = ["NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"]
        
        for agent_id in agent_ids:
            mock_agent = Mock()
            mock_agent.agent_id = agent_id
            mock_agent.process = Mock(side_effect=self._mock_agent_process)
            mock_agent.reflect = Mock(return_value={"reflection": f"Mock reflection for {agent_id}"})
            mock_agents[agent_id] = mock_agent
        
        return mock_agents
    
    def _mock_agent_process(self, state):
        """Mock agent process method"""
        # Simulate agent processing by updating step count and adding output
        state.step_count += 1
        
        # Add mock output based on agent type
        mock_output = {
            "processed": True,
            "timestamp": state.current_date,
            "agent_step": state.step_count
        }
        
        return state
    
    def test_initialize_data_node(self):
        """Test initialize_data_node function"""
        state = LangGraphState(
            current_date="2025-01-15",
            ticker="AAPL",
            run_id="test_run"
        )
        
        # Execute node
        updated_state = self.coordinator.initialize_data_node(state)
        
        # Verify state was updated
        self.assertIsInstance(updated_state, LangGraphState)
        self.assertEqual(updated_state.current_date, "2025-01-15")
        self.assertEqual(updated_state.cash, 100000)
        self.assertIsInstance(updated_state.ohlcv_data, dict)
        self.assertIsInstance(updated_state.news_data, list)
    
    def test_check_data_availability_node(self):
        """Test check_data_availability_node function"""
        state = LangGraphState(
            current_date="2025-01-15",
            ticker="AAPL",
            run_id="test_run"
        )
        
        # Add some test data
        state.ohlcv_data = {"AAPL": pd.DataFrame({"Close": [150.0]})}
        state.news_data = [{"title": "Test news"}]
        state.fundamental_data = {"AAPL": {"pe_ratio": 25.0}}
        
        # Execute node
        updated_state = self.coordinator.check_data_availability_node(state)
        
        # Verify availability was checked
        self.assertIn("data_availability", updated_state.config)
        availability = updated_state.config["data_availability"]
        
        self.assertTrue(availability["price_data"])
        self.assertTrue(availability["news_data"])
        self.assertTrue(availability["fundamental_data"])
    
    def test_check_data_availability_node_no_data(self):
        """Test check_data_availability_node with no data"""
        state = LangGraphState(
            current_date="2025-01-15",
            ticker="AAPL",
            run_id="test_run"
        )
        
        # Execute node with empty data
        updated_state = self.coordinator.check_data_availability_node(state)
        
        # Verify availability was checked
        availability = updated_state.config["data_availability"]
        
        self.assertFalse(availability["price_data"])
        self.assertFalse(availability["news_data"])
        self.assertFalse(availability["fundamental_data"])
    
    def test_naa_node(self):
        """Test NAA (News Analyst Agent) node"""
        state = LangGraphState(
            current_date="2025-01-15",
            ticker="AAPL",
            news_data=[{"title": "Test news", "summary": "Test"}],
            run_id="test_run"
        )
        
        # Set data availability to enable NAA execution
        state.config["data_availability"] = {"news_data": True}
        
        initial_step_count = state.step_count
        
        # Execute node
        updated_state = self.coordinator.naa_node(state)
        
        # Verify agent was called
        self.coordinator.agents["NAA"].process.assert_called_once_with(state)
        
        # Verify state was updated
        self.assertEqual(updated_state.step_count, initial_step_count + 1)
    
    def test_taa_node(self):
        """Test TAA (Technical Analyst Agent) node"""
        state = LangGraphState(
            current_date="2025-01-15",
            ticker="AAPL",
            ohlcv_data={"AAPL": pd.DataFrame({"Close": [150.0, 151.0]})},
            run_id="test_run"
        )
        
        # Set data availability to enable TAA execution
        state.config["data_availability"] = {"price_data": True}
        
        initial_step_count = state.step_count
        
        # Execute node
        updated_state = self.coordinator.taa_node(state)
        
        # Verify agent was called
        self.coordinator.agents["TAA"].process.assert_called_once_with(state)
        
        # Verify state was updated
        self.assertEqual(updated_state.step_count, initial_step_count + 1)
    
    def test_faa_node(self):
        """Test FAA (Fundamental Analyst Agent) node"""
        state = LangGraphState(
            current_date="2025-01-15",
            ticker="AAPL",
            fundamental_data={"AAPL": {"pe_ratio": 25.0}},
            run_id="test_run"
        )
        
        # Set data availability to enable FAA execution
        state.config["data_availability"] = {"fundamental_data": True}
        
        initial_step_count = state.step_count
        
        # Execute node
        updated_state = self.coordinator.faa_node(state)
        
        # Verify agent was called
        self.coordinator.agents["FAA"].process.assert_called_once_with(state)
        
        # Verify state was updated
        self.assertEqual(updated_state.step_count, initial_step_count + 1)
    
    def test_outlook_agents_nodes(self):
        """Test outlook agent nodes (BOA, BeOA, NOA)"""
        state = LangGraphState(
            current_date="2025-01-15",
            ticker="AAPL",
            run_id="test_run"
        )
        
        # Add some analysis outputs
        state.naa_output = {"sentiment": "positive"}
        state.taa_output = {"trend": "bullish"}
        state.faa_output = {"valuation": "fair"}
        
        # Test BOA node
        updated_state = self.coordinator.boa_node(state)
        self.coordinator.agents["BOA"].process.assert_called_once_with(state)
        
        # Test BeOA node
        updated_state = self.coordinator.beoa_node(updated_state)
        self.coordinator.agents["BeOA"].process.assert_called_once_with(updated_state)
        
        # Test NOA node
        updated_state = self.coordinator.noa_node(updated_state)
        self.coordinator.agents["NOA"].process.assert_called_once_with(updated_state)
    
    def test_tra_node(self):
        """Test TRA (Trader Agent) node"""
        state = LangGraphState(
            current_date="2025-01-15",
            ticker="AAPL",
            cash=100000.0,
            run_id="test_run"
        )
        
        # Add outlook outputs
        state.boa_output = {"recommendation": "buy"}
        state.beoa_output = {"recommendation": "hold"}
        state.noa_output = {"recommendation": "hold"}
        
        initial_step_count = state.step_count
        
        # Execute node
        updated_state = self.coordinator.tra_node(state)
        
        # Verify agent was called
        self.coordinator.agents["TRA"].process.assert_called_once_with(state)
        
        # Verify state was updated
        self.assertEqual(updated_state.step_count, initial_step_count + 1)
    
    def test_collect_analysis_node(self):
        """Test collect_analysis_node function"""
        state = LangGraphState(
            current_date="2025-01-15",
            ticker="AAPL",
            run_id="test_run"
        )
        
        # Add some agent outputs
        state.naa_output = {"sentiment": "positive"}
        state.taa_output = {"trend": "bullish"}
        state.faa_output = {"valuation": "fair"}
        
        # Execute node
        updated_state = self.coordinator.collect_analysis_node(state)
        
        # Verify collection was performed
        self.assertIn("analyses_completed", updated_state.config)
        analyses_completed = updated_state.config["analyses_completed"]
        
        # Should have counted 3 analyses
        self.assertEqual(analyses_completed, 3)
    
    def test_finalize_results_node(self):
        """Test finalize_results_node function"""
        state = LangGraphState(
            current_date="2025-01-15",
            ticker="AAPL",
            cash=95000.0,
            positions={"AAPL": 10},
            run_id="test_run"
        )
        
        # Add trading history
        state.trading_history = [
            {"action": "BUY", "ticker": "AAPL", "quantity": 10, "price": 150.0}
        ]
        
        # Execute node
        updated_state = self.coordinator.finalize_results_node(state)
        
        # Verify finalization was performed
        self.assertIn("final_portfolio_value", updated_state.config)
        self.assertIn("execution_stats", updated_state.config)
        
        final_value = updated_state.config["final_portfolio_value"]
        execution_stats = updated_state.config["execution_stats"]
        
        self.assertEqual(final_value, 95000.0)
        self.assertIsInstance(execution_stats, dict)
    
    def test_route_base_analysts(self):
        """Test route_base_analysts routing function"""
        # Test with data available
        state = LangGraphState(
            current_date="2025-01-15",
            ticker="AAPL",
            run_id="test_run"
        )
        
        state.config["data_availability"] = {
            "price_data": True,
            "news_data": True,
            "fundamental_data": False
        }
        
        route = self.coordinator.route_base_analysts(state)
        self.assertEqual(route, "run_base_analysts")
        
        # Test with no data available
        state.config["data_availability"] = {
            "price_data": False,
            "news_data": False,
            "fundamental_data": False
        }
        
        route = self.coordinator.route_base_analysts(state)
        self.assertEqual(route, "skip_to_collection")
    
    def test_route_to_optimization(self):
        """Test route_to_optimization routing function"""
        state = LangGraphState(
            current_date="2025-01-15",
            ticker="AAPL",
            run_id="test_run"
        )
        
        # Test with optimization disabled
        self.coordinator.enable_optimization = False
        self.coordinator.enable_myerson_calculation = False
        
        route = self.coordinator.route_to_optimization(state)
        self.assertEqual(route, "finish")
        
        # Test with optimization enabled
        self.coordinator.enable_optimization = True
        
        route = self.coordinator.route_to_optimization(state)
        self.assertEqual(route, "optimize")
    
    def test_node_error_handling(self):
        """Test error handling in nodes"""
        # Create agent that raises exception
        failing_agent = Mock()
        failing_agent.process = Mock(side_effect=Exception("Agent processing error"))
        self.coordinator.agents["NAA"] = failing_agent
        
        state = LangGraphState(
            current_date="2025-01-15",
            ticker="AAPL",
            news_data=[{"title": "Test"}],
            run_id="test_run"
        )
        
        # Node should handle error gracefully
        updated_state = self.coordinator.naa_node(state)
        
        # State should still be returned (error handling in _execute_agent)
        self.assertIsInstance(updated_state, LangGraphState)
    
    def test_state_consistency_across_nodes(self):
        """Test that state remains consistent across multiple node executions"""
        state = LangGraphState(
            current_date="2025-01-15",
            ticker="AAPL",
            cash=100000.0,
            positions={},
            run_id="test_run"
        )
        
        # Add test data
        state.ohlcv_data = {"AAPL": pd.DataFrame({"Close": [150.0]})}
        state.news_data = [{"title": "Test news"}]
        state.fundamental_data = {"AAPL": {"pe_ratio": 25.0}}
        
        # Execute multiple nodes in sequence
        state = self.coordinator.initialize_data_node(state)
        state = self.coordinator.check_data_availability_node(state)
        state = self.coordinator.naa_node(state)
        state = self.coordinator.taa_node(state)
        state = self.coordinator.faa_node(state)
        
        # Verify core state fields are preserved
        self.assertEqual(state.current_date, "2025-01-15")
        self.assertEqual(state.ticker, "AAPL")
        self.assertEqual(state.run_id, "test_run")
        
        # Verify step count increased
        self.assertGreater(state.step_count, 0)


if __name__ == '__main__':
    unittest.main() 