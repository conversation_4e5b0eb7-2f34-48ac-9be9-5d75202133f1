#!/usr/bin/env python3
"""
Unit tests for BaseAgent class

Tests the core functionality of the BaseAgent class including
LangGraphState compatibility, model interface integration,
and basic agent operations.
"""

import unittest
import os
import sys
import json
import tempfile
import shutil
from unittest.mock import Mock, patch, MagicMock

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from agents.base_agent import BaseAgent
from utils.langgraph_state import LangGraphState


class MockAgent(BaseAgent):
    """Mock agent implementation for testing BaseAgent functionality"""
    
    def process(self, state: LangGraphState) -> LangGraphState:
        """Mock process method"""
        state.step_count += 1
        return state
    
    def reflect(self, reward: float, state: LangGraphState) -> dict:
        """Mock reflect method"""
        return {
            "reflection": f"Received reward: {reward}",
            "agent_id": self.agent_id,
            "state_step": state.step_count
        }


class TestBaseAgent(unittest.TestCase):
    """Test cases for BaseAgent class"""
    
    def setUp(self):
        """Set up test fixtures"""
        # Create mock model interface
        self.mock_model = Mock()
        self.mock_model.get_completion = Mock(return_value="Mock response")
        
        # Create temporary directory for logs
        self.temp_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.temp_dir)
        
        # Create test agent
        self.agent = MockAgent("TEST_AGENT", "test_agent", self.mock_model)
        
    def tearDown(self):
        """Clean up test fixtures"""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.temp_dir)
    
    def test_agent_initialization(self):
        """Test agent initialization"""
        self.assertEqual(self.agent.agent_id, "TEST_AGENT")
        self.assertEqual(self.agent.agent_type, "test_agent")
        self.assertEqual(self.agent.model_interface, self.mock_model)
        self.assertIsInstance(self.agent.memory, list)
        self.assertIsInstance(self.agent.history, list)
        self.assertIsInstance(self.agent.prompt_history, list)
    
    def test_agent_initialization_without_model(self):
        """Test agent initialization without model interface"""
        with patch('agents.base_agent.default_model', None):
            with self.assertRaises(ValueError):
                MockAgent("TEST_AGENT", "test_agent", None)
    
    def test_agent_initialization_with_invalid_model(self):
        """Test agent initialization with invalid model interface"""
        invalid_model = Mock()
        # Remove get_completion method
        del invalid_model.get_completion
        
        with self.assertRaises(ValueError):
            MockAgent("TEST_AGENT", "test_agent", invalid_model)
    
    def test_process_method_with_langgraph_state(self):
        """Test process method with LangGraphState"""
        # Create test state
        state = LangGraphState(
            current_date="2025-01-15",
            ticker="AAPL",
            run_id="test_run"
        )
        
        initial_step_count = state.step_count
        
        # Process state
        updated_state = self.agent.process(state)
        
        # Verify state was updated
        self.assertIsInstance(updated_state, LangGraphState)
        self.assertEqual(updated_state.step_count, initial_step_count + 1)
        self.assertEqual(updated_state.ticker, "AAPL")
        self.assertEqual(updated_state.current_date, "2025-01-15")
    
    def test_reflect_method(self):
        """Test reflect method"""
        state = LangGraphState(
            current_date="2025-01-15",
            ticker="AAPL",
            run_id="test_run",
            step_count=5
        )
        
        reward = 0.75
        reflection = self.agent.reflect(reward, state)
        
        self.assertIsInstance(reflection, dict)
        self.assertIn("reflection", reflection)
        self.assertIn("agent_id", reflection)
        self.assertEqual(reflection["agent_id"], "TEST_AGENT")
        self.assertEqual(reflection["state_step"], 5)
    
    def test_memory_operations(self):
        """Test memory operations"""
        # Test adding to memory
        test_item = {"test": "data", "timestamp": "2025-01-15"}
        self.agent.add_to_memory(test_item)
        
        self.assertEqual(len(self.agent.memory), 1)
        self.assertEqual(self.agent.memory[0], test_item)
        
        # Test clearing memory
        self.agent.clear_memory()
        self.assertEqual(len(self.agent.memory), 0)
    
    def test_history_operations(self):
        """Test history operations"""
        # Add some history
        self.agent.history.append("test_entry")
        self.assertEqual(len(self.agent.history), 1)
        
        # Clear history
        self.agent.clear_history()
        self.assertEqual(len(self.agent.history), 0)
    
    def test_log_interaction(self):
        """Test interaction logging"""
        prompt = "Test prompt"
        response = "Test response"
        metadata = {"test_key": "test_value"}
        
        self.agent.log_interaction(prompt, response, metadata)
        
        # Check if log file was created
        log_files = [f for f in os.listdir(self.agent.log_dir) if f.startswith("TEST_AGENT_")]
        self.assertEqual(len(log_files), 1)
        
        # Check log content
        log_file_path = os.path.join(self.agent.log_dir, log_files[0])
        with open(log_file_path, 'r') as f:
            log_data = json.load(f)
        
        self.assertEqual(log_data["agent_id"], "TEST_AGENT")
        self.assertEqual(log_data["agent_type"], "test_agent")
        self.assertEqual(log_data["prompt"], prompt)
        self.assertEqual(log_data["response"], response)
        self.assertEqual(log_data["metadata"], metadata)
    
    def test_save_and_load_memory(self):
        """Test memory save and load operations"""
        run_id = "test_run_123"
        
        # Add test data to memory
        test_data = [
            {"type": "analysis", "data": "test_analysis"},
            {"type": "decision", "data": "test_decision"}
        ]
        
        for item in test_data:
            self.agent.add_to_memory(item)
        
        # Save memory
        success = self.agent.save_memory(run_id)
        self.assertTrue(success)
        
        # Clear memory and load it back
        self.agent.clear_memory()
        self.assertEqual(len(self.agent.memory), 0)
        
        success = self.agent.load_memory(run_id)
        self.assertTrue(success)
        self.assertEqual(len(self.agent.memory), 2)
        self.assertEqual(self.agent.memory, test_data)
    
    def test_load_nonexistent_memory(self):
        """Test loading memory that doesn't exist"""
        success = self.agent.load_memory("nonexistent_run")
        self.assertFalse(success)
    
    def test_prompt_history(self):
        """Test prompt history functionality"""
        initial_history = self.agent.get_prompt_history()
        self.assertIsInstance(initial_history, list)
        self.assertEqual(len(initial_history), 0)
    
    def test_serializable_data_handling(self):
        """Test handling of serializable data"""
        # Test with complex data structure
        complex_data = {
            "string": "test",
            "number": 123,
            "list": [1, 2, 3],
            "nested": {"key": "value"}
        }
        
        serializable = self.agent._ensure_serializable(complex_data)
        
        # Should be able to serialize without error
        json_str = json.dumps(serializable)
        self.assertIsInstance(json_str, str)
    
    def test_default_prompt_template(self):
        """Test default prompt template"""
        template = self.agent.get_default_prompt_template()
        self.assertIsInstance(template, str)
        self.assertGreater(len(template), 0)


class TestAgentWithLangGraphState(unittest.TestCase):
    """Test agent integration with LangGraphState"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.mock_model = Mock()
        self.mock_model.get_completion = Mock(return_value="Mock response")
        
        self.temp_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.temp_dir)
        
        self.agent = MockAgent("TEST_AGENT", "test_agent", self.mock_model)
    
    def tearDown(self):
        """Clean up test fixtures"""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.temp_dir)
    
    def test_state_preservation(self):
        """Test that agent preserves important state information"""
        state = LangGraphState(
            current_date="2025-01-15",
            ticker="AAPL",
            cash=100000.0,
            positions={"AAPL": 10},
            run_id="test_run"
        )
        
        # Process state
        updated_state = self.agent.process(state)
        
        # Verify important fields are preserved
        self.assertEqual(updated_state.current_date, "2025-01-15")
        self.assertEqual(updated_state.ticker, "AAPL")
        self.assertEqual(updated_state.cash, 100000.0)
        self.assertEqual(updated_state.positions, {"AAPL": 10})
        self.assertEqual(updated_state.run_id, "test_run")
    
    def test_state_modification(self):
        """Test that agent can modify state appropriately"""
        state = LangGraphState(
            current_date="2025-01-15",
            ticker="AAPL",
            run_id="test_run",
            step_count=0
        )
        
        # Process state multiple times
        for i in range(3):
            state = self.agent.process(state)
        
        # Verify step count was incremented
        self.assertEqual(state.step_count, 3)


if __name__ == '__main__':
    unittest.main() 