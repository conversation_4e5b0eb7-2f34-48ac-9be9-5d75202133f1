#!/usr/bin/env python3
"""
Unit tests for NewsAnalystAgent

Tests the news analysis functionality including news data processing,
sentiment analysis, and LangGraphState integration.
"""

import unittest
import os
import sys
import json
import tempfile
import shutil
from unittest.mock import Mock, patch

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from agents.news_analyst import NewsAnalystAgent
from utils.langgraph_state import LangGraphState


class TestNewsAnalystAgent(unittest.TestCase):
    """Test cases for NewsAnalystAgent"""
    
    def setUp(self):
        """Set up test fixtures"""
        # Create mock model interface
        self.mock_model = Mock()
        self.mock_model.get_completion = Mock(return_value="Mock reflection response")
        self.mock_model.get_structured_completion = Mock(return_value={
            "key_events": ["Test event 1", "Test event 2"],
            "market_sentiment_score": 7,
            "ticker_specific_impact": "Positive impact on AAPL",
            "key_investment_factors": ["Strong earnings", "Market expansion"],
            "confidence_rating": 4
        })
        
        # Create temporary directory for logs
        self.temp_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.temp_dir)
        
        # Create test agent
        self.agent = NewsAnalystAgent("NAA", "news_analyst", self.mock_model)
        
    def tearDown(self):
        """Clean up test fixtures"""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.temp_dir)
    
    def test_agent_initialization(self):
        """Test agent initialization"""
        self.assertEqual(self.agent.agent_id, "NAA")
        self.assertEqual(self.agent.agent_type, "news_analyst")
        self.assertIn("sentiment_instructions", self.agent.prompt_params)
        self.assertIn("continuity_instructions", self.agent.prompt_params)
        self.assertIn("impact_instructions", self.agent.prompt_params)
        
        # Check output format
        self.assertIn("key_events", self.agent.output_format)
        self.assertIn("market_sentiment_score", self.agent.output_format)
        self.assertIn("ticker_specific_impact", self.agent.output_format)
        self.assertIn("key_investment_factors", self.agent.output_format)
        self.assertIn("confidence_rating", self.agent.output_format)
    
    def test_format_news_data(self):
        """Test news data formatting"""
        news_data = [
            {
                "title": "Apple Reports Strong Q4 Earnings",
                "time_published": "2025-01-15T10:30:00",
                "summary": "Apple exceeded expectations with strong iPhone sales",
                "source": "Reuters"
            },
            {
                "title": "Tech Stocks Rally",
                "time_published": "2025-01-15T11:00:00",
                "summary": "Technology sector shows strong performance",
                "source": "Bloomberg"
            }
        ]
        
        formatted = self.agent._format_news_data(news_data)
        
        self.assertIsInstance(formatted, str)
        self.assertIn("Apple Reports Strong Q4 Earnings", formatted)
        self.assertIn("Tech Stocks Rally", formatted)
        self.assertIn("Reuters", formatted)
        self.assertIn("Bloomberg", formatted)
    
    def test_format_news_data_with_missing_fields(self):
        """Test news data formatting with missing fields"""
        news_data = [
            {
                "title": "Incomplete News Item"
                # Missing other fields
            }
        ]
        
        formatted = self.agent._format_news_data(news_data)
        
        self.assertIsInstance(formatted, str)
        self.assertIn("Incomplete News Item", formatted)
        self.assertIn("无摘要", formatted)
        self.assertIn("未知来源", formatted)
    
    def test_format_news_data_limit(self):
        """Test that news data formatting limits to 20 items"""
        # Create 25 news items
        news_data = []
        for i in range(25):
            news_data.append({
                "title": f"News Item {i+1}",
                "time_published": "2025-01-15T10:30:00",
                "summary": f"Summary for news item {i+1}",
                "source": "Test Source"
            })
        
        formatted = self.agent._format_news_data(news_data)
        
        # Should only contain first 20 items
        self.assertIn("News Item 1", formatted)
        self.assertIn("News Item 20", formatted)
        self.assertNotIn("News Item 21", formatted)
    
    def test_process_with_news_data(self):
        """Test processing with valid news data"""
        # Create test state with news data
        news_data = [
            {
                "title": "Apple Reports Strong Q4 Earnings",
                "time_published": "2025-01-15T10:30:00",
                "summary": "Apple exceeded expectations with strong iPhone sales",
                "source": "Reuters"
            }
        ]
        
        state = LangGraphState(
            current_date="2025-01-15",
            ticker="AAPL",
            news_data=news_data,
            run_id="test_run"
        )
        
        # Process state
        updated_state = self.agent.process(state)
        
        # Verify model was called
        self.mock_model.get_structured_completion.assert_called_once()
        
        # Verify state was updated
        naa_output = updated_state.get_agent_output("NAA")
        self.assertIsInstance(naa_output, dict)
        self.assertIn("key_events", naa_output)
        self.assertIn("market_sentiment_score", naa_output)
        self.assertEqual(naa_output["market_sentiment_score"], 7)
    
    def test_process_without_news_data(self):
        """Test processing without news data"""
        state = LangGraphState(
            current_date="2025-01-15",
            ticker="AAPL",
            news_data=[],  # Empty news data
            run_id="test_run"
        )
        
        # Process state
        updated_state = self.agent.process(state)
        
        # Verify model was NOT called for structured completion
        self.mock_model.get_structured_completion.assert_not_called()
        
        # Verify empty analysis was returned
        naa_output = updated_state.get_agent_output("NAA")
        self.assertIsInstance(naa_output, dict)
        self.assertEqual(naa_output["key_events"], ["无新闻数据可分析"])
        self.assertEqual(naa_output["market_sentiment_score"], 5)
        self.assertEqual(naa_output["confidence_rating"], 1)
    
    def test_process_with_none_news_data(self):
        """Test processing with None news data"""
        state = LangGraphState(
            current_date="2025-01-15",
            ticker="AAPL",
            news_data=None,  # None news data
            run_id="test_run"
        )
        
        # Process state
        updated_state = self.agent.process(state)
        
        # Verify empty analysis was returned
        naa_output = updated_state.get_agent_output("NAA")
        self.assertIsInstance(naa_output, dict)
        self.assertEqual(naa_output["key_events"], ["无新闻数据可分析"])
    
    def test_reflect_method(self):
        """Test reflect method"""
        state = LangGraphState(
            current_date="2025-01-15",
            ticker="AAPL",
            run_id="test_run"
        )
        
        reward = 0.8
        reflection = self.agent.reflect(reward, state)
        
        # Verify model was called for reflection
        self.mock_model.get_completion.assert_called_once()
        
        # Verify reflection structure
        self.assertIsInstance(reflection, dict)
        self.assertIn("reflection", reflection)
        
        # Verify reflection was added to memory
        self.assertEqual(len(self.agent.memory), 1)
        self.assertEqual(self.agent.memory[0]["reward"], 0.8)
        self.assertIn("reflection", self.agent.memory[0])
    
    def test_prompt_template_formatting(self):
        """Test prompt template formatting"""
        news_data = [
            {
                "title": "Test News",
                "time_published": "2025-01-15T10:30:00",
                "summary": "Test summary",
                "source": "Test Source"
            }
        ]
        
        formatted_news = self.agent._format_news_data(news_data)
        
        prompt = self.agent._format_prompt(
            self.agent.analysis_template,
            ticker="AAPL",
            news_data=formatted_news
        )
        
        self.assertIsInstance(prompt, str)
        self.assertIn("AAPL", prompt)
        self.assertIn("Test News", prompt)
        self.assertIn("professional US stock market news analyst", prompt)
    
    def test_state_preservation(self):
        """Test that important state fields are preserved"""
        state = LangGraphState(
            current_date="2025-01-15",
            ticker="AAPL",
            cash=100000.0,
            positions={"AAPL": 10},
            news_data=[{"title": "Test", "summary": "Test"}],
            run_id="test_run"
        )
        
        # Process state
        updated_state = self.agent.process(state)
        
        # Verify important fields are preserved
        self.assertEqual(updated_state.current_date, "2025-01-15")
        self.assertEqual(updated_state.ticker, "AAPL")
        self.assertEqual(updated_state.cash, 100000.0)
        self.assertEqual(updated_state.positions, {"AAPL": 10})
        self.assertEqual(updated_state.run_id, "test_run")
    
    def test_model_interface_error_handling(self):
        """Test handling of model interface errors"""
        # Create agent with failing model
        failing_model = Mock()
        failing_model.get_structured_completion = Mock(side_effect=Exception("Model error"))
        
        agent = NewsAnalystAgent("NAA", "news_analyst", failing_model)
        
        state = LangGraphState(
            current_date="2025-01-15",
            ticker="AAPL",
            news_data=[{"title": "Test", "summary": "Test"}],
            run_id="test_run"
        )
        
        # Process should handle the error gracefully
        with self.assertRaises(Exception):
            agent.process(state)
    
    def test_output_format_validation(self):
        """Test that output format is correctly structured"""
        # Test with custom model response
        custom_response = {
            "key_events": ["Event 1", "Event 2", "Event 3"],
            "market_sentiment_score": 8,
            "ticker_specific_impact": "Very positive impact",
            "key_investment_factors": ["Factor 1", "Factor 2"],
            "confidence_rating": 5
        }
        
        self.mock_model.get_structured_completion.return_value = custom_response
        
        state = LangGraphState(
            current_date="2025-01-15",
            ticker="AAPL",
            news_data=[{"title": "Test", "summary": "Test"}],
            run_id="test_run"
        )
        
        updated_state = self.agent.process(state)
        naa_output = updated_state.get_agent_output("NAA")
        
        # Verify all required fields are present
        required_fields = ["key_events", "market_sentiment_score", "ticker_specific_impact", 
                          "key_investment_factors", "confidence_rating"]
        
        for field in required_fields:
            self.assertIn(field, naa_output)
        
        # Verify data types and values
        self.assertIsInstance(naa_output["key_events"], list)
        self.assertIsInstance(naa_output["market_sentiment_score"], int)
        self.assertIsInstance(naa_output["ticker_specific_impact"], str)
        self.assertIsInstance(naa_output["key_investment_factors"], list)
        self.assertIsInstance(naa_output["confidence_rating"], int)


if __name__ == '__main__':
    unittest.main() 