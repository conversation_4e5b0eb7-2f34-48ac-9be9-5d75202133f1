#!/usr/bin/env python3
"""
Test runner for the Multi-Agent Trading System

Runs all unit tests, integration tests, and generates a comprehensive test report.
"""

import subprocess
import sys
import os
from datetime import datetime


def run_command(command, description):
    """Run a command and return the result"""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {command}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(
            command.split(),
            capture_output=True,
            text=True,
            cwd=os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        )
        
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        
        return result.returncode == 0, result.stdout, result.stderr
    
    except Exception as e:
        print(f"Error running command: {e}")
        return False, "", str(e)


def main():
    """Run all tests and generate report"""
    print("🧪 Multi-Agent Trading System - Test Suite")
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    test_results = []
    
    # Test configurations
    tests = [
        {
            "command": "python -m pytest tests/unit/ -v --tb=short",
            "description": "Unit Tests",
            "category": "unit"
        },
        {
            "command": "python -m pytest tests/integration/ -v --tb=short",
            "description": "Integration Tests", 
            "category": "integration"
        },
        {
            "command": "python -m pytest tests/ -v --tb=short --cov=agents --cov=utils --cov=langgraph_coordinator --cov-report=term-missing",
            "description": "Full Test Suite with Coverage",
            "category": "coverage"
        }
    ]
    
    # Run each test category
    for test in tests:
        success, stdout, stderr = run_command(test["command"], test["description"])
        
        test_results.append({
            "category": test["category"],
            "description": test["description"],
            "success": success,
            "stdout": stdout,
            "stderr": stderr
        })
    
    # Generate summary report
    print(f"\n{'='*80}")
    print("📊 TEST SUMMARY REPORT")
    print(f"{'='*80}")
    
    total_tests = len(test_results)
    passed_tests = sum(1 for result in test_results if result["success"])
    
    print(f"Total Test Categories: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    print(f"\n{'='*60}")
    print("DETAILED RESULTS:")
    print(f"{'='*60}")
    
    for result in test_results:
        status = "✅ PASSED" if result["success"] else "❌ FAILED"
        print(f"{status} - {result['description']}")
        
        if not result["success"] and result["stderr"]:
            print(f"   Error: {result['stderr'][:200]}...")
    
    # Overall result
    all_passed = all(result["success"] for result in test_results)
    
    print(f"\n{'='*80}")
    if all_passed:
        print("🎉 ALL TESTS PASSED! The system is ready for deployment.")
        return 0
    else:
        print("⚠️  SOME TESTS FAILED. Please review the failures above.")
        return 1


if __name__ == "__main__":
    sys.exit(main()) 