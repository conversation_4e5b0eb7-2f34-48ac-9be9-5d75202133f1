# 多股票数据集 - 使用指南

## 数据集概述

本数据集包含 **NVDA、AAPL、MSFT、META** 四只重点股票过去一年的完整历史数据，为多智能体交易系统提供数据支持。

## 数据集结构

### 数据存储位置
- **根目录**: `data/tickers/`
- **每个股票独立数据库**: `data/tickers/{TICKER}/{TICKER}_data.db`

### 数据库文件
```
data/tickers/
├── NVDA/
│   └── NVDA_data.db (16.81 MB)
├── AAPL/
│   └── AAPL_data.db (11.26 MB)
├── MSFT/
│   └── MSFT_data.db (2.41 MB)
└── META/
    └── META_data.db (0.35 MB)
```

## 数据内容详情

### 1. NVDA (英伟达) - 100% 完整性 ✅
- **OHLCV数据**: 6,430 条记录 (1999-11-01 至 2025-05-23)
- **新闻数据**: 8,164 篇文章 (30个新闻源)
- **财务数据**: 年度60份，季度243份报告
- **数据质量**: 优秀，无异常数据

### 2. AAPL (苹果) - 100% 完整性 ✅
- **OHLCV数据**: 6,430 条记录 (1999-11-01 至 2025-05-23)
- **新闻数据**: 5,052 篇文章 (28个新闻源)
- **财务数据**: 年度60份，季度243份报告
- **数据质量**: 优秀，无异常数据

### 3. MSFT (微软) - 100% 完整性 ✅
- **OHLCV数据**: 6,427 条记录 (1999-11-01 至 2025-05-20)
- **新闻数据**: 802 篇文章 (33个新闻源)
- **财务数据**: 年度60份，季度81份报告
- **数据质量**: 优秀，无异常数据

### 4. META (Meta平台) - 35.3% 完整性 ⚠️
- **OHLCV数据**: 3,272 条记录 (2012-05-18 至 2025-05-22)
- **新闻数据**: 0 篇文章 (因API限制未完成)
- **财务数据**: 0份报告 (因API限制未完成)
- **备注**: 由于API限制，需后续补充新闻和财务数据

## 数据表结构

### OHLCV 表 (`ohlcv`)
```sql
CREATE TABLE ohlcv (
    ticker TEXT NOT NULL,
    trade_date DATE NOT NULL,
    Open REAL,
    High REAL,
    Low REAL,
    Close REAL,
    Adj_Close REAL,
    Volume INTEGER,
    PRIMARY KEY (ticker, trade_date)
);
```

### 新闻表 (`news`)
```sql
CREATE TABLE news (
    article_id TEXT PRIMARY KEY,
    title TEXT,
    url TEXT,
    time_published DATETIME,
    authors TEXT,          -- JSON格式
    summary TEXT,
    banner_image TEXT,
    source TEXT,
    category_within_source TEXT,
    source_domain TEXT,
    topics TEXT,           -- JSON格式
    overall_sentiment_score REAL,
    overall_sentiment_label TEXT,
    ticker_sentiment JSON  -- JSON格式
);
```

### 财务数据表 (`annual_financials`, `quarterly_financials`)
```sql
CREATE TABLE annual_financials (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ticker TEXT NOT NULL,
    fiscal_date DATE NOT NULL,
    report_type TEXT NOT NULL, -- 'income', 'balance', 'cashflow'
    reported_currency TEXT,
    data_json TEXT NOT NULL,   -- 完整JSON数据
    retrieved_at DATETIME NOT NULL,
    UNIQUE (ticker, fiscal_date, report_type)
);
```

## 使用方法

### 1. 数据访问示例

#### Python SQLite 连接
```python
import sqlite3
import pandas as pd

# 连接到特定股票的数据库
def connect_to_ticker_db(ticker):
    db_path = f"data/tickers/{ticker}/{ticker}_data.db"
    return sqlite3.connect(db_path)

# 获取OHLCV数据
def get_ohlcv_data(ticker, start_date=None, end_date=None):
    conn = connect_to_ticker_db(ticker)
    
    query = """
    SELECT * FROM ohlcv 
    WHERE ticker = ?
    """
    params = [ticker]
    
    if start_date:
        query += " AND trade_date >= ?"
        params.append(start_date)
    if end_date:
        query += " AND trade_date <= ?"
        params.append(end_date)
        
    query += " ORDER BY trade_date"
    
    df = pd.read_sql_query(query, conn, params=params)
    conn.close()
    return df

# 获取新闻数据
def get_news_data(ticker, limit=100):
    conn = connect_to_ticker_db(ticker)
    
    query = """
    SELECT title, time_published, summary, source, 
           overall_sentiment_score, overall_sentiment_label
    FROM news 
    ORDER BY time_published DESC 
    LIMIT ?
    """
    
    df = pd.read_sql_query(query, conn, params=[limit])
    conn.close()
    return df
```

#### 使用示例
```python
# 获取NVDA最近一个月的价格数据
nvda_prices = get_ohlcv_data('NVDA', '2025-04-01', '2025-05-01')

# 获取AAPL最新100条新闻
aapl_news = get_news_data('AAPL', 100)

print(f"NVDA价格数据: {len(nvda_prices)} 条记录")
print(f"AAPL新闻数据: {len(aapl_news)} 条记录")
```

### 2. 与多智能体系统集成

#### 运行单股票分析
```bash
# 对NVDA进行完整分析
python run_multi_agent.py NVDA

# 使用优化版本
python run_multi_agent_optimized.py NVDA
```

### 3. 数据管理工具

#### 数据验证
```bash
# 验证所有股票数据
python data/validate_dataset.py --detailed

# 验证单个股票
python data/validate_dataset.py --ticker NVDA --detailed
```

#### 恢复下载
```bash
# 恢复所有中断的下载
python data/resume_data_download.py --verbose

# 恢复单个股票
python data/resume_data_download.py --ticker META --verbose
```

#### 重新构建数据集
```bash
# 完整重建数据集
python data/build_dataset.py --verbose

# 指定日期范围
python data/build_dataset.py --start-date 2024-01-01 --end-date 2024-12-31
```

## 数据质量说明

### 优势
1. **历史数据丰富**: OHLCV数据覆盖20+年历史
2. **新闻数据多样**: 来源包括Benzinga、Motley Fool、Zacks等权威财经媒体
3. **情感分析完整**: 每篇新闻都有情感得分和标签
4. **财务数据详细**: 包含损益表、资产负债表、现金流量表

### 已知限制
1. **API限制**: Alpha Vantage免费版每日25次请求限制
2. **META数据不完整**: 受API限制影响，新闻和财务数据缺失
3. **实时性**: 数据更新需要手动运行脚本

### 数据更新策略
- **日常更新**: 运行 `resume_data_download.py` 继续未完成的下载
- **完整更新**: 每月运行 `build_dataset.py` 进行全量更新
- **单股票更新**: 使用 `get_all_data.py <TICKER> <START> <END>` 更新特定股票

## 技术规格

- **数据库**: SQLite 3.x
- **编程语言**: Python 3.8+
- **数据源**: Alpha Vantage API
- **总大小**: ~31 MB
- **记录总数**: 约 25,000+ 条 OHLCV记录，14,000+ 篇新闻文章

## 故障排除

### 常见问题

1. **数据库连接失败**
   ```python
   # 检查文件是否存在
   import os
   db_path = "data/tickers/NVDA/NVDA_data.db"
   print(f"数据库存在: {os.path.exists(db_path)}")
   ```

2. **API限制错误**
   - 错误信息包含 "rate limit" 或 "25 requests per day"
   - 解决方案: 等待24小时后重试，或升级API计划

3. **数据不完整**
   ```bash
   # 检查数据状态
   python data/validate_dataset.py --ticker NVDA
   
   # 恢复下载
   python data/resume_data_download.py --ticker NVDA
   ```

### 联系方式
如遇到技术问题，请检查脚本输出的错误信息，大多数问题都与API限制相关。

---

**最后更新**: 2025-05-27  
**版本**: 1.0  
**维护者**: 多智能体交易系统团队 