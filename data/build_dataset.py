#!/usr/bin/env python3
"""
批量数据获取脚本 - 构建多股票数据集

专门为NVDA、AAPL、MSFT、META这四只重点股票获取过去一年的完整数据，
包括OHLCV、新闻和财务报表数据。

用法: python data/build_dataset.py [--verbose] [--start-date YYYY-MM-DD] [--end-date YYYY-MM-DD]
示例: python data/build_dataset.py --verbose
"""

import os
import sys
import argparse
import subprocess
import time
from datetime import datetime, timedelta, date
from typing import List, Dict, Any
import sqlite3

# Add project root to sys.path
current_script_path = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(current_script_path))
sys.path.append(project_root)

# Import from config
try:
    from config import DATA_DIR, ALPHAVANTAGE_API_KEY
except ImportError:
    print("错误: config.py 未找到或必需变量未定义。", file=sys.stderr)
    print("请确保 config.py 存在并定义了 DATA_DIR, ALPHAVANTAGE_API_KEY。", file=sys.stderr)
    sys.exit(1)

# 目标股票列表
TARGET_STOCKS = ["NVDA", "AAPL", "MSFT", "META"]

# 日期格式
DATE_FORMAT = '%Y-%m-%d'

def get_database_path(ticker):
    """根据ticker获取对应的数据库路径"""
    ticker_dir = os.path.join(DATA_DIR, "tickers", ticker.upper())
    os.makedirs(ticker_dir, exist_ok=True)
    return os.path.join(ticker_dir, f"{ticker.upper()}_data.db")

def get_default_date_range():
    """获取默认的日期范围（过去一年）"""
    end_date = date.today()
    start_date = end_date - timedelta(days=365)
    return start_date.strftime(DATE_FORMAT), end_date.strftime(DATE_FORMAT)

def check_api_configuration():
    """检查API配置是否正确"""
    if not ALPHAVANTAGE_API_KEY or ALPHAVANTAGE_API_KEY == "YOUR_ALPHA_VANTAGE_API_KEY":
        print("错误: 请在 config.py 中配置您的 Alpha Vantage API 密钥。", file=sys.stderr)
        return False
    return True

def run_data_script(script_name: str, args: List[str], verbose: bool = False) -> bool:
    """运行数据获取脚本"""
    script_path = os.path.join(os.path.dirname(__file__), script_name)
    cmd = [sys.executable, script_path] + args
    
    if verbose:
        print(f"执行命令: {' '.join(cmd)}", file=sys.stderr)
    
    try:
        result = subprocess.run(cmd, text=True, encoding='utf-8')
        if result.returncode != 0:
            print(f"{script_name} 执行失败，返回码: {result.returncode}", file=sys.stderr)
            return False
        if verbose:
            print(f"{script_name} 执行成功", file=sys.stderr)
        return True
    except FileNotFoundError:
        print(f"错误: 脚本 {script_path} 未找到", file=sys.stderr)
        return False
    except Exception as e:
        print(f"执行 {script_name} 时发生错误: {e}", file=sys.stderr)
        return False

def get_data_summary(ticker: str) -> Dict[str, Any]:
    """获取指定股票的数据概要"""
    db_path = get_database_path(ticker)
    
    if not os.path.exists(db_path):
        return {"exists": False}
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        summary = {"exists": True}
        
        # 检查OHLCV数据
        cursor.execute("SELECT COUNT(*), MIN(trade_date), MAX(trade_date) FROM ohlcv WHERE ticker = ?", (ticker,))
        ohlcv_result = cursor.fetchone()
        summary["ohlcv"] = {
            "count": ohlcv_result[0] if ohlcv_result[0] else 0,
            "date_range": f"{ohlcv_result[1]} 至 {ohlcv_result[2]}" if ohlcv_result[1] and ohlcv_result[2] else "无数据"
        }
        
        # 检查新闻数据
        cursor.execute("SELECT COUNT(*), MIN(date(time_published)), MAX(date(time_published)) FROM news")
        news_result = cursor.fetchone()
        summary["news"] = {
            "count": news_result[0] if news_result[0] else 0,
            "date_range": f"{news_result[1]} 至 {news_result[2]}" if news_result[1] and news_result[2] else "无数据"
        }
        
        # 检查年度财务数据
        cursor.execute("SELECT COUNT(*), MIN(fiscal_date), MAX(fiscal_date) FROM annual_financials WHERE ticker = ?", (ticker,))
        annual_result = cursor.fetchone()
        summary["annual_financials"] = {
            "count": annual_result[0] if annual_result[0] else 0,
            "date_range": f"{annual_result[1]} 至 {annual_result[2]}" if annual_result[1] and annual_result[2] else "无数据"
        }
        
        # 检查季度财务数据
        cursor.execute("SELECT COUNT(*), MIN(fiscal_date), MAX(fiscal_date) FROM quarterly_financials WHERE ticker = ?", (ticker,))
        quarterly_result = cursor.fetchone()
        summary["quarterly_financials"] = {
            "count": quarterly_result[0] if quarterly_result[0] else 0,
            "date_range": f"{quarterly_result[1]} 至 {quarterly_result[2]}" if quarterly_result[1] and quarterly_result[2] else "无数据"
        }
        
        conn.close()
        return summary
        
    except sqlite3.Error as e:
        print(f"查询 {ticker} 数据库时出错: {e}", file=sys.stderr)
        return {"exists": True, "error": str(e)}

def build_dataset(start_date: str, end_date: str, verbose: bool = False):
    """构建数据集的主函数"""
    
    print("=" * 60)
    print("多股票数据集构建开始")
    print("=" * 60)
    print(f"目标股票: {', '.join(TARGET_STOCKS)}")
    print(f"日期范围: {start_date} 至 {end_date}")
    print(f"数据存储目录: {DATA_DIR}")
    print("=" * 60)
    
    # 检查API配置
    if not check_api_configuration():
        return False
    
    # 确保数据目录存在
    os.makedirs(DATA_DIR, exist_ok=True)
    os.makedirs(os.path.join(DATA_DIR, "tickers"), exist_ok=True)
    
    success_count = 0
    total_stocks = len(TARGET_STOCKS)
    
    for i, ticker in enumerate(TARGET_STOCKS, 1):
        print(f"\n{'='*50}")
        print(f"处理股票 {i}/{total_stocks}: {ticker}")
        print(f"{'='*50}")
        
        try:
            # 使用 get_all_data.py 一站式获取所有数据
            if run_data_script('get_all_data.py', [ticker, start_date, end_date] + (['--verbose'] if verbose else []), verbose):
                success_count += 1
                print(f"✓ {ticker} 数据获取成功")
            else:
                print(f"✗ {ticker} 数据获取失败")
                
        except Exception as e:
            print(f"✗ 处理 {ticker} 时发生未知错误: {e}", file=sys.stderr)
        
        # 在处理下一个股票前稍作延迟，避免API速率限制
        if i < total_stocks:
            print(f"等待 10 秒后处理下一个股票...")
            time.sleep(10)
    
    print(f"\n{'='*60}")
    print("数据集构建完成")
    print(f"成功: {success_count}/{total_stocks} 只股票")
    print(f"{'='*60}")
    
    # 显示数据概要
    print("\n数据概要:")
    print("-" * 60)
    for ticker in TARGET_STOCKS:
        print(f"\n{ticker}:")
        summary = get_data_summary(ticker)
        if not summary["exists"]:
            print("  数据库不存在")
            continue
        if "error" in summary:
            print(f"  查询错误: {summary['error']}")
            continue
            
        print(f"  OHLCV数据: {summary['ohlcv']['count']} 条记录, {summary['ohlcv']['date_range']}")
        print(f"  新闻数据: {summary['news']['count']} 条记录, {summary['news']['date_range']}")
        print(f"  年度财务: {summary['annual_financials']['count']} 条记录, {summary['annual_financials']['date_range']}")
        print(f"  季度财务: {summary['quarterly_financials']['count']} 条记录, {summary['quarterly_financials']['date_range']}")
        
        db_path = get_database_path(ticker)
        print(f"  数据库路径: {db_path}")
    
    return success_count == total_stocks

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="批量构建多股票数据集")
    parser.add_argument("--verbose", action="store_true", help="启用详细输出")
    parser.add_argument("--start-date", type=str, help="开始日期 (YYYY-MM-DD)")
    parser.add_argument("--end-date", type=str, help="结束日期 (YYYY-MM-DD)")
    args = parser.parse_args()
    
    # 确定日期范围
    if args.start_date and args.end_date:
        start_date, end_date = args.start_date, args.end_date
        try:
            # 验证日期格式
            datetime.strptime(start_date, DATE_FORMAT)
            datetime.strptime(end_date, DATE_FORMAT)
        except ValueError:
            print(f"错误: 日期格式无效。请使用 {DATE_FORMAT} 格式。", file=sys.stderr)
            sys.exit(1)
    else:
        start_date, end_date = get_default_date_range()
        print(f"使用默认日期范围: {start_date} 至 {end_date}")
    
    # 构建数据集
    success = build_dataset(start_date, end_date, args.verbose)
    
    if success:
        print("\n✓ 数据集构建完全成功！")
        print("\n下一步:")
        print("1. 可以使用 SQLite 工具或 Python 脚本查看和分析数据")
        print("2. 运行多智能体交易系统进行股票分析")
        print("3. 例如: python run_multi_agent.py NVDA")
        sys.exit(0)
    else:
        print("\n✗ 数据集构建部分失败，请检查上面的错误信息。")
        sys.exit(1)

if __name__ == "__main__":
    main() 