#!/usr/bin/env python3
"""
检查数据连续性脚本
"""
import sqlite3
import os
import sys
from datetime import datetime, timedelta

# Add project root to sys.path
current_script_path = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(current_script_path))
sys.path.append(project_root)

from config import DATA_DIR

def check_data_continuity(ticker, start_date, end_date):
    """检查指定股票在指定时间范围内的数据连续性"""
    db_path = os.path.join(DATA_DIR, 'tickers', ticker, f'{ticker}_data.db')
    print(f'检查数据库: {db_path}')
    print(f'数据库存在: {os.path.exists(db_path)}')
    
    if not os.path.exists(db_path):
        print(f'{ticker}数据库文件不存在!')
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 检查指定时间范围的数据
    cursor.execute('''
        SELECT trade_date, Close, Volume 
        FROM ohlcv 
        WHERE ticker = ? 
        AND trade_date >= ? 
        AND trade_date <= ?
        ORDER BY trade_date
    ''', (ticker, start_date, end_date))
    
    results = cursor.fetchall()
    print(f'\n找到 {len(results)} 条{ticker}数据记录 ({start_date} 到 {end_date})')
    
    if results:
        print('\n前10条记录:')
        for i, (date, close, volume) in enumerate(results[:10]):
            print(f'  {date}: 收盘价 ${close}, 成交量 {volume:,}')
        
        if len(results) > 10:
            print('\n后10条记录:')
            for i, (date, close, volume) in enumerate(results[-10:]):
                print(f'  {date}: 收盘价 ${close}, 成交量 {volume:,}')
    
    # 检查是否有数据间隙
    if len(results) > 1:
        print('\n检查数据连续性...')
        gaps = []
        for i in range(1, len(results)):
            prev_date = datetime.strptime(results[i-1][0], '%Y-%m-%d')
            curr_date = datetime.strptime(results[i][0], '%Y-%m-%d')
            diff = (curr_date - prev_date).days
            if diff > 3:  # 超过3天的间隙（考虑周末）
                gaps.append((results[i-1][0], results[i][0], diff))
        
        if gaps:
            print(f'发现 {len(gaps)} 个数据间隙:')
            for prev, curr, days in gaps:
                print(f'  {prev} -> {curr} (间隔 {days} 天)')
        else:
            print('数据连续性良好')
    
    # 检查数据库中的总体时间范围
    cursor.execute('''
        SELECT MIN(trade_date), MAX(trade_date), COUNT(*)
        FROM ohlcv 
        WHERE ticker = ?
    ''', (ticker,))
    
    min_date, max_date, total_count = cursor.fetchone()
    print(f'\n{ticker}数据库总体信息:')
    print(f'  总记录数: {total_count}')
    print(f'  最早日期: {min_date}')
    print(f'  最晚日期: {max_date}')
    
    conn.close()

if __name__ == "__main__":
    ticker = "AAPL"
    start_date = "2024-05-01"
    end_date = "2024-09-01"
    
    if len(sys.argv) > 1:
        ticker = sys.argv[1].upper()
    if len(sys.argv) > 2:
        start_date = sys.argv[2]
    if len(sys.argv) > 3:
        end_date = sys.argv[3]
    
    check_data_continuity(ticker, start_date, end_date) 