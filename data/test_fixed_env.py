#!/usr/bin/env python3
"""
测试修复后的交易环境
"""
import os
import sys
from datetime import datetime

# Add project root to sys.path
current_script_path = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(current_script_path))
sys.path.append(project_root)

from stock_trading_env import StockTradingEnv

def test_fixed_env():
    """测试修复后的交易环境"""
    
    print("=" * 60)
    print("测试修复后的交易环境")
    print("=" * 60)
    
    # 配置
    config = {
        "start_date": "2024-05-01",
        "end_date": "2024-09-01", 
        "stocks": ["AAPL"],
        "starting_cash": 100000,
        "trading_fee_rate": 0.001
    }
    
    print(f"配置: {config}")
    
    try:
        # 创建环境
        print("\n1. 创建交易环境...")
        env = StockTradingEnv(config)
        
        print(f"✅ 交易环境创建成功")
        print(f"   总交易日数: {env.total_days}")
        print(f"   交易日范围: {env.trading_days[0].strftime('%Y-%m-%d')} 到 {env.trading_days[-1].strftime('%Y-%m-%d')}")
        
        # 重置环境
        print("\n2. 重置环境...")
        state, reward, done, info = env.reset()
        print(f"✅ 环境重置成功")
        print(f"   当前日期: {env.current_date.strftime('%Y-%m-%d')}")
        print(f"   current_day_idx: {env.current_day_idx}")
        print(f"   初始现金: ${info['starting_cash']:,.2f}")
        
        # 测试步进
        print("\n3. 测试步进逻辑...")
        steps_to_test = min(10, env.total_days - 1)
        
        for step in range(steps_to_test):
            # 简单的买入动作
            action = {"AAPL": 1.0 if step == 0 else 0.0}
            
            prev_date = env.current_date.strftime('%Y-%m-%d')
            prev_idx = env.current_day_idx
            
            state, reward, done, info = env.step(action)
            
            curr_date = env.current_date.strftime('%Y-%m-%d') if not done else "DONE"
            curr_idx = env.current_day_idx
            
            print(f"   步骤 {step+1}: {prev_date} (idx:{prev_idx}) -> {curr_date} (idx:{curr_idx})")
            print(f"      净值: ${info['net_worth']:,.2f}, 日收益率: {info['daily_return']:.4f}")
            
            if done:
                print(f"   ✅ 模拟完成，共运行 {step+1} 步")
                break
        
        # 验证数据连续性
        print("\n4. 验证数据连续性...")
        print(f"   AAPL价格数据长度: {len(env.price_data['AAPL'])}")
        print(f"   交易日数量: {env.total_days}")
        
        if len(env.price_data['AAPL']) == env.total_days:
            print("   ✅ 数据长度匹配")
        else:
            print(f"   ⚠️ 数据长度不匹配: {len(env.price_data['AAPL'])} vs {env.total_days}")
        
        # 显示一些价格数据样本
        if not env.price_data['AAPL'].empty:
            print("\n   价格数据样本:")
            df = env.price_data['AAPL']
            for i in range(min(5, len(df))):
                row = df.iloc[i]
                print(f"     {i}: {row['date'].strftime('%Y-%m-%d')} - 收盘价 ${row['close']:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_fixed_env()
    if success:
        print("\n🎉 测试通过！交易环境修复成功。")
    else:
        print("\n❌ 测试失败，需要进一步修复。")
        sys.exit(1) 