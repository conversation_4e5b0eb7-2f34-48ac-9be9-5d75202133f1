#!/usr/bin/env python3
"""
调试交易日设置脚本
"""
import pandas as pd
import sqlite3
import os
import sys
from datetime import datetime

# Add project root to sys.path
current_script_path = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(current_script_path))
sys.path.append(project_root)

from config import DATA_DIR

def debug_trading_days():
    """调试交易日设置逻辑"""
    
    # 模拟交易环境的日期范围设置
    start_date = "2024-05-01"
    end_date = "2024-09-01"
    
    print("=" * 60)
    print("调试交易环境日期设置")
    print("=" * 60)
    print(f"请求的日期范围: {start_date} 到 {end_date}")
    
    # 1. 检查pandas生成的工作日
    print("\n1. Pandas工作日生成:")
    date_range = pd.date_range(start=start_date, end=end_date, freq='B')
    print(f"   生成的交易日数量: {len(date_range)}")
    print(f"   前10个交易日:")
    for i, date in enumerate(date_range[:10]):
        print(f"     {i}: {date.strftime('%Y-%m-%d')} ({date.strftime('%A')})")
    
    if len(date_range) > 10:
        print(f"   后10个交易日:")
        for i, date in enumerate(date_range[-10:], len(date_range)-10):
            print(f"     {i}: {date.strftime('%Y-%m-%d')} ({date.strftime('%A')})")
    
    # 2. 检查数据库中的实际交易日
    print("\n2. 数据库中的实际交易日:")
    db_path = os.path.join(DATA_DIR, 'tickers', 'AAPL', 'AAPL_data.db')
    if os.path.exists(db_path):
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT trade_date 
            FROM ohlcv 
            WHERE ticker = 'AAPL' 
            AND trade_date >= ? 
            AND trade_date <= ?
            ORDER BY trade_date
        ''', (start_date, end_date))
        
        db_dates = [row[0] for row in cursor.fetchall()]
        print(f"   数据库中的交易日数量: {len(db_dates)}")
        print(f"   前10个交易日:")
        for i, date in enumerate(db_dates[:10]):
            date_obj = pd.to_datetime(date)
            print(f"     {i}: {date} ({date_obj.strftime('%A')})")
        
        if len(db_dates) > 10:
            print(f"   后10个交易日:")
            for i, date in enumerate(db_dates[-10:], len(db_dates)-10):
                date_obj = pd.to_datetime(date)
                print(f"     {i}: {date} ({date_obj.strftime('%A')})")
        
        conn.close()
        
        # 3. 对比分析
        print("\n3. 对比分析:")
        pandas_dates = set(date.strftime('%Y-%m-%d') for date in date_range)
        db_dates_set = set(db_dates)
        
        # 找出pandas有但数据库没有的日期
        missing_in_db = pandas_dates - db_dates_set
        if missing_in_db:
            print(f"   Pandas生成但数据库缺失的交易日 ({len(missing_in_db)}个):")
            for date in sorted(missing_in_db)[:10]:  # 只显示前10个
                date_obj = pd.to_datetime(date)
                print(f"     {date} ({date_obj.strftime('%A')})")
        
        # 找出数据库有但pandas没有的日期
        extra_in_db = db_dates_set - pandas_dates
        if extra_in_db:
            print(f"   数据库有但Pandas未生成的交易日 ({len(extra_in_db)}个):")
            for date in sorted(extra_in_db)[:10]:  # 只显示前10个
                date_obj = pd.to_datetime(date)
                print(f"     {date} ({date_obj.strftime('%A')})")
        
        # 检查交易环境实际会如何加载数据
        print("\n4. 交易环境数据加载模拟:")
        print("   查询条件: 数据库日期在pandas工作日范围内")
        print(f"   start_date_str: {date_range[0].strftime('%Y-%m-%d')}")
        print(f"   end_date_str: {date_range[-1].strftime('%Y-%m-%d')}")
        
        # 模拟交易环境的查询
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute('''
            SELECT ticker, trade_date, Open, High, Low, Close, Adj_Close, Volume
            FROM ohlcv 
            WHERE ticker = ? AND trade_date BETWEEN ? AND ?
            ORDER BY trade_date ASC
        ''', ('AAPL', date_range[0].strftime('%Y-%m-%d'), date_range[-1].strftime('%Y-%m-%d')))
        
        env_data = cursor.fetchall()
        print(f"   交易环境将加载的数据条数: {len(env_data)}")
        
        if env_data:
            print(f"   前5条:")
            for i, row in enumerate(env_data[:5]):
                print(f"     {i}: {row[1]} - 收盘价 ${row[5]}")
            
            if len(env_data) > 5:
                print(f"   后5条:")
                for i, row in enumerate(env_data[-5:], len(env_data)-5):
                    print(f"     {i}: {row[1]} - 收盘价 ${row[5]}")
        
        conn.close()
        
        # 5. 关键问题分析
        print("\n5. 关键问题分析:")
        print(f"   - 交易环境使用 pandas.date_range 生成 {len(date_range)} 个工作日")
        print(f"   - 数据库中实际有 {len(db_dates)} 个交易日")
        print(f"   - 但交易环境只能访问pandas工作日范围内的数据: {len(env_data)} 条")
        
        if len(env_data) < len(db_dates):
            print(f"   ⚠️  问题: 交易环境错误地限制了数据范围!")
            print(f"      应该使用数据库中的实际交易日，而不是pandas的工作日")
        
    else:
        print("   ❌ AAPL数据库不存在")

if __name__ == "__main__":
    debug_trading_days() 