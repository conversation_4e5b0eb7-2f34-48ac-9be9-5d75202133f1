#!/usr/bin/env python3
"""
增强日志系统测试脚本
测试修复后的多智能体系统，包含详细的调试日志
"""

import sys
import os
import argparse
from datetime import datetime
import json

def test_enhanced_logging():
    """测试增强的日志系统"""
    
    print("🧪 测试增强的日志系统...")
    
    # 运行一个简短的多智能体测试
    cmd = [
        "python", "run_multi_agent.py",
        "--tickers", "AAPL",
        "--days", "3",
        "--start_date", "2025-01-01",
        "--end_date", "2025-03-31",
        "--verbose"
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    import subprocess
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        print("=== 标准输出 ===")
        print(result.stdout)
        
        if result.stderr:
            print("=== 标准错误 ===")
            print(result.stderr)
        
        print(f"返回码: {result.returncode}")
        
        # 检查日志文件
        check_log_files()
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("❌ 测试超时")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def check_log_files():
    """检查生成的日志文件"""
    
    print("\n📋 检查日志文件...")
    
    # 查找最新的日志目录
    logs_dir = "logs/langgraph_run"
    if not os.path.exists(logs_dir):
        print("❌ 日志目录不存在")
        return
    
    # 获取最新的运行目录
    run_dirs = [d for d in os.listdir(logs_dir) if os.path.isdir(os.path.join(logs_dir, d))]
    if not run_dirs:
        print("❌ 没有找到运行日志")
        return
    
    latest_run = sorted(run_dirs)[-1]
    run_log_dir = os.path.join(logs_dir, latest_run)
    
    print(f"📁 检查日志目录: {run_log_dir}")
    
    # 检查各种日志文件
    log_files = [
        "workflow_states.jsonl",
        "FAA_input.jsonl",
        "FAA_output.jsonl",
        "TAA_input.jsonl", 
        "TAA_output.jsonl",
        "TRA_input.jsonl",
        "TRA_output.jsonl"
    ]
    
    for log_file in log_files:
        log_path = os.path.join(run_log_dir, log_file)
        if os.path.exists(log_path):
            print(f"✅ 找到日志文件: {log_file}")
            
            # 显示文件内容摘要
            try:
                with open(log_path, 'r') as f:
                    lines = f.readlines()
                    print(f"   📊 {len(lines)} 行记录")
                    
                    if lines:
                        # 显示第一条记录
                        first_record = json.loads(lines[0])
                        print(f"   🕐 首条记录时间: {first_record.get('timestamp', 'N/A')}")
                        
                        if len(lines) > 1:
                            last_record = json.loads(lines[-1])
                            print(f"   🕐 末条记录时间: {last_record.get('timestamp', 'N/A')}")
                            
            except Exception as e:
                print(f"   ❌ 读取日志文件失败: {e}")
        else:
            print(f"❌ 缺少日志文件: {log_file}")

def main():
    """主函数"""
    
    parser = argparse.ArgumentParser(description="测试增强的日志系统")
    parser.add_argument("--check-logs-only", action="store_true", 
                       help="只检查现有日志文件")
    
    args = parser.parse_args()
    
    if args.check_logs_only:
        check_log_files()
    else:
        success = test_enhanced_logging()
        if success:
            print("\n✅ 测试成功完成")
        else:
            print("\n❌ 测试失败")
            sys.exit(1)

if __name__ == "__main__":
    main()
