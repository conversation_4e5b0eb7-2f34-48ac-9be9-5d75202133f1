"""
基础智能体类

为所有智能体提供基础功能和接口
"""
import os
import json
import time
from typing import Dict, Any, List, Optional, Union
from abc import ABC, abstractmethod

# 导入模型接口
try:
    from utils.model import default_model
    # 这里我们直接使用 utils.model，假设项目根目录在 sys.path 中
except ImportError:
    # 如果 utils.model 无法导入，说明模型接口缺失，抛出错误
    # raise ValueError("未提供模型接口，且无法导入默认模型接口") # 原始错误信息
    # 考虑到 model_interface 可以通过参数传入，如果导入失败，可以将 model_interface 设为 None，并在后续检查
    default_model = None # 如果导入失败，将 default_model 设为 None
    print("警告: 无法导入默认模型接口 utils.model")

# 导入 LangGraphState
try:
    from utils.langgraph_state import LangGraphState
except ImportError:
    print("警告: 无法导入 LangGraphState，使用字典类型作为后备")

class BaseAgent(ABC):
    """
    基础智能体抽象类
    
    所有智能体的父类，提供基本接口和功能
    """
    
    def __init__(self, agent_id: str, agent_type: str, model_interface=None):
        """
        初始化基础智能体
        
        参数:
            agent_id: 智能体唯一标识符
            agent_type: 智能体类型
            model_interface: 模型接口，用于与LLM交互
        """
        self.agent_id = agent_id
        self.agent_type = agent_type
        
        # System prompt for the agent
        self.system_prompt = self.get_default_prompt_template()
        
        # 如果没有提供模型接口，使用默认接口（如果已导入）
        if model_interface is None:
            if default_model is not None:
                 self.model_interface = default_model
            else:
                 raise ValueError("未提供模型接口，且默认模型接口 utils.model 未加载成功")
        else:
            self.model_interface = model_interface
            
        # 确保模型接口有 get_completion 方法
        if not hasattr(self.model_interface, 'get_completion') or not callable(self.model_interface.get_completion):
             raise ValueError(f"提供的模型接口 {type(self.model_interface).__name__} 没有 'get_completion' 方法")
            
        # 智能体提示参数
        self.prompt_params = {}
        
        # 智能体记忆
        self.memory = []
        
        # 智能体历史
        self.history = []
        
        # 智能体提示词历史，用于记录每次生成的最终提示词及其参数
        self.prompt_history = []
        
        # 智能体日志目录
        self.log_dir = os.path.join("logs", agent_type)
        os.makedirs(self.log_dir, exist_ok=True)
        
        # 当前使用的提示模板
        self.current_prompt_template = self.get_default_prompt_template()
        
    def log_interaction(self, prompt: str, response: str, metadata: Dict[str, Any] = None) -> None:
        """
        记录交互日志
        
        参数:
            prompt: 提示
            response: 模型响应
            metadata: 其他元数据
        """
        timestamp = time.time()
        formatted_time = time.strftime("%Y%m%d_%H%M%S", time.localtime(timestamp))
        log_file = os.path.join(self.log_dir, f"{self.agent_id}_{formatted_time}.log")
        
        log_data = {
            "agent_id": self.agent_id,
            "agent_type": self.agent_type,
            "timestamp": timestamp,
            "prompt": prompt,
            "response": response
        }
        
        if metadata:
            # 确保元数据可序列化
            metadata = self._ensure_serializable(metadata)
            log_data["metadata"] = metadata
            
        with open(log_file, "w") as f:
            json.dump(log_data, f, indent=2)
            
    def save_memory(self, run_id: str) -> bool:
        """
        保存智能体记忆
        
        参数:
            run_id: 运行ID
            
        返回:
            是否成功保存
        """
        memory_dir = os.path.join("logs", run_id, "memory")
        os.makedirs(memory_dir, exist_ok=True)
        
        memory_file = os.path.join(memory_dir, f"{self.agent_id}.json")
        try:
            # 确保记忆可序列化
            serializable_memory = self._ensure_serializable(self.memory)
            with open(memory_file, "w") as f:
                json.dump(serializable_memory, f, indent=2)
            return True
        except Exception as e:
            print(f"保存记忆失败: {e}")
            return False
            
    def load_memory(self, run_id: str) -> bool:
        """
        加载智能体记忆
        
        参数:
            run_id: 运行ID
            
        返回:
            是否成功加载
        """
        memory_file = os.path.join("logs", run_id, "memory", f"{self.agent_id}.json")
        if os.path.exists(memory_file):
            try:
                with open(memory_file, "r") as f:
                    self.memory = json.load(f)
                return True
            except Exception as e:
                print(f"加载记忆失败: {e}")
                return False
        return False
        
    def add_to_memory(self, item: Any) -> None:
        """
        添加项目到记忆
        
        参数:
            item: 要添加的项目
        """
        self.memory.append(item)
        
    def clear_memory(self) -> None:
        """清空记忆"""
        self.memory = []
        
    def clear_history(self) -> None:
        """清空历史"""
        self.history = []
        
    def get_prompt_history(self) -> List[Dict[str, Any]]:
        """
        获取智能体的提示词历史
        
        返回:
            包含提示词历史记录的列表
        """
        return self.prompt_history
        
    @abstractmethod
    def process(self, state: 'LangGraphState') -> 'LangGraphState':
        """
        处理输入状态并生成输出
        
        参数:
            state: LangGraphState输入状态
            
        返回:
            更新后的LangGraphState
        """
        pass
        
    @abstractmethod
    def reflect(self, reward: float, state: 'LangGraphState') -> Dict[str, Any]:
        """
        基于奖励和状态进行反思
        
        参数:
            reward: 奖励值
            state: 当前LangGraphState状态
            
        返回:
            反思结果
        """
        pass
        
    def _get_system_message(self) -> str:
        """
        获取系统消息
        
        返回:
            系统消息字符串
        """
        return self.system_prompt
        
    def _format_prompt(self, template: str, **kwargs) -> str:
        """
        格式化提示模板
        
        参数:
            template: 提示模板
            **kwargs: 格式化参数
            
        返回:
            格式化后的提示
        """
        import re
        
        # 合并提示参数和传入参数
        format_args = {**self.prompt_params}
        format_args.update(kwargs)
        
        # 修复：清理模板中的不配对花括号和其他格式化问题
        try:
            # 1. 检查并清理不配对的花括号
            cleaned_template = self._clean_template_braces(template)
            
            # 2. 清理format_args中的值，确保它们不包含会干扰格式化的字符
            cleaned_format_args = {}
            for key, value in format_args.items():
                if isinstance(value, str):
                    # 移除或转义字符串值中的花括号，避免格式化冲突
                    cleaned_value = value.replace('{', '{{').replace('}', '}}')
                    cleaned_format_args[key] = cleaned_value
                else:
                    cleaned_format_args[key] = value
                    
            formatted_prompt = cleaned_template.format(**cleaned_format_args)
            
        except (ValueError, KeyError) as e:
            print(f"⚠️ 模板格式化出错: {e}")
            print(f"   模板: {template[:200]}...")
            print(f"   参数键: {list(format_args.keys())}")
            
            # 如果格式化失败，使用更安全的方式
            # 简单地将参数作为字符串附加到模板末尾
            formatted_prompt = template + "\n\n参数:\n"
            for key, value in format_args.items():
                formatted_prompt += f"{key}: {str(value)[:100]}...\n"
        
        # 记录生成的提示词及其参数
        self.prompt_history.append({
            "timestamp": time.time(),
            "prompt": formatted_prompt,
            "prompt_params_at_time": {**self.prompt_params} # 记录当时的prompt_params
        })
        
        return formatted_prompt
        
    def _clean_template_braces(self, template: str) -> str:
        """
        清理模板中的不配对花括号
        
        参数:
            template: 原始模板字符串
            
        返回:
            清理后的模板字符串
        """
        import re
        
        # 1. 先处理连续的花括号（可能是转义的）
        # {{ 和 }} 应该保持不变
        
        # 2. 查找所有单个的花括号
        # 这个正则表达式匹配不是连续的单个花括号
        single_open_braces = re.findall(r'(?<!\{)\{(?!\{)', template)
        single_close_braces = re.findall(r'(?<!\})\}(?!\})', template)
        
        # 3. 检查是否有不配对的花括号
        if len(single_open_braces) != len(single_close_braces):
            print(f"⚠️ 检测到不配对的花括号: {len(single_open_braces)} 个 '{{' vs {len(single_close_braces)} 个 '}}'")
            
            # 移除所有单个的花括号（保留双花括号转义）
            # 先标记双花括号
            template = template.replace('{{', '__DOUBLE_OPEN__')
            template = template.replace('}}', '__DOUBLE_CLOSE__')
            
            # 移除单个花括号
            template = template.replace('{', '').replace('}', '')
            
            # 恢复双花括号
            template = template.replace('__DOUBLE_OPEN__', '{{')
            template = template.replace('__DOUBLE_CLOSE__', '}}')
            
            print(f"✅ 已清理不配对的花括号")
            
        return template

    def get_default_prompt_template(self) -> str:
        """
        获取默认提示模板
        
        如果子类有analysis_template属性，则返回该属性；
        如果有_build_prompt方法，则尝试获取该方法生成的模板。
        子类可以覆盖此方法以提供自定义的默认模板。
        
        返回:
            默认提示模板字符串
        """
        # 优先检查是否有analysis_template属性
        if hasattr(self, 'analysis_template'):
            return getattr(self, 'analysis_template')
            
        # 如果上述方法失败，返回一个基本的默认模板
        return f"""
You are a professional {self.agent_type} agent with ID {self.agent_id}.
Please analyze the provided data and generate a professional analysis report.
"""
    
    def _ensure_serializable(self, data: Any) -> Any:
        """
        确保数据是JSON可序列化的
        
        参数:
            data: 要检查的数据
            
        返回:
            JSON可序列化后的数据副本
        """
        try:
            # 尝试直接序列化和反序列化
            return json.loads(json.dumps(data))
        except (TypeError, json.JSONDecodeError):
            # 如果失败，尝试更深层次的清理
            if isinstance(data, dict):
                return {k: self._ensure_serializable(v) for k, v in data.items()}
            elif isinstance(data, list):
                return [self._ensure_serializable(item) for item in data]
            elif isinstance(data, (str, int, float, bool, type(None))):
                return data
            else:
                # 对于不可序列化的对象，尝试转换为字符串表示
                try:
                    return str(data)
                except Exception:
                    return None # 或其他默认值

    def _extract_json(self, text: str) -> str:
        """
        从文本中提取JSON字符串并清理
        
        参数:
            text: 输入文本
            
        返回:
            提取出的JSON字符串或空字符串
        """
        json_str = ""
        
        # 1. 尝试提取代码块中的内容 (优先)
        if "```json" in text:
            start = text.find("```json") + 7
            end = text.find("```", start)
            if end > start:
                json_str = text[start:end].strip()
        elif "```" in text:
            start = text.find("```") + 3
            end = text.find("```", start)
            if end > start:
                json_str = text[start:end].strip()
        
        # 2. 如果没有从代码块中提取到，尝试使用正则表达式查找JSON对象或数组
        if not json_str:
            import re
            # 查找第一个 JSON 对象 {...} 或 JSON 数组 [...]
            # 使用原始字符串来避免反斜杠转义问题
            match_object = re.search(r'\{.*\}', text, re.DOTALL)
            match_array = re.search(r'\[.*\]', text, re.DOTALL)
            
            if match_object and match_array:
                # 如果同时找到对象和数组，选择更长的那一个（可能是更完整的JSON）
                if len(match_object.group(0)) > len(match_array.group(0)):
                    json_str = match_object.group(0)
                else:
                    json_str = match_array.group(0)
            elif match_object:
                json_str = match_object.group(0)
            elif match_array:
                json_str = match_array.group(0)

        # 3. 清理提取出的JSON字符串（移除注释行）
        if json_str:
            lines = json_str.split('\n')
            cleaned_lines = []
            for line in lines:
                # 移除 // 注释
                comment_pos = line.find('//')
                if comment_pos >= 0:
                    line = line[:comment_pos].strip()
                
                # 跳过空行
                if line.strip():
                    cleaned_lines.append(line)
            json_str = '\n'.join(cleaned_lines)
        
        return json_str.strip() 