"""
技术分析智能体

分析股票价格走势和技术指标，提供技术面分析
"""
import json
from typing import Dict, Any, List, Optional
from .base_agent import BaseAgent
from utils.langgraph_state import LangGraphState

class TechnicalAnalystAgent(BaseAgent):
    """
    技术分析智能体
    
    分析股票价格走势和技术指标，提供技术面分析
    """
    
    def __init__(self, agent_id: str, agent_type: str, model_interface=None):
        """
        初始化技术面分析智能体
        
        参数:
            agent_id: 智能体ID
            agent_type: 智能体类型
            model_interface: 模型接口
        """
        super().__init__(agent_id, agent_type, model_interface)
        
        # 设置默认提示参数
        self.prompt_params = {
            "emphasis_on_trend": "Focus on identifying key trend reversals and continuation patterns.",
            "time_window_guidance": "Consider both short-term (1-2 days) and medium-term (5-7 days) trends.",
            "technical_indicator_focus": "Pay special attention to SMA crosses, MACD signals, and RSI."
        }
        
        # 技术分析模板
        self.analysis_template = """
You are a professional US stock market technical analyst. Analyze the following price data and technical indicators, and provide technical analysis.

{emphasis_on_trend}
{time_window_guidance}
{technical_indicator_focus}

Here is the price and technical indicator data to analyze:
{price_data}

Please provide your analysis in the following structured format:
1. Trend Analysis (Primary trend, support and resistance levels)
2. Key Technical Indicator Signals (Moving Averages, MACD, RSI, etc.)
3. Pattern Recognition (Head and shoulders, triangles, flags, etc.)
4. Trading Recommendation (Buy, sell, or hold recommendation based on technicals)

Ensure your analysis is concise, professional, and insightful.
"""

        # 结构化输出格式
        self.output_format = {
            "trend_analysis": {
                "primary_trend": "",  # 上升/下降/横盘
                "strength": "",       # 强/中/弱
                "support_levels": [],  # 支撑位列表
                "resistance_levels": []  # 阻力位列表
            },
            "technical_signals": {
                "moving_averages": "",  # 看涨/看跌/中性
                "macd": "",            # 看涨/看跌/中性
                "rsi": {
                    "value": 0,
                    "interpretation": ""  # 超买/超卖/中性
                },
                "other_indicators": {}
            },
            "patterns": [
                {
                    "pattern_type": "",  # 形态类型
                    "confidence": "",    # 高/中/低
                    "target_price": 0    # 目标价格
                }
            ],
            "trading_recommendation": {
                "action": "",  # 买入/卖出/持有
                "time_frame": "",  # 短期/中期/长期
                "reasoning": ""
            }
        }
        
    def process(self, state: LangGraphState) -> LangGraphState:
        """
        处理价格数据并生成技术分析报告
        
        参数:
            state: 包含价格数据的LangGraphState
            
        返回:
            更新后的LangGraphState
        """
        # 提取价格数据
        ticker = state.ticker
        price_data = state.ohlcv_data.get(ticker, {})
        
        # 如果没有价格数据，返回空分析
        if not price_data or (hasattr(price_data, 'empty') and price_data.empty):
            empty_analysis = {
                "trend_analysis": {
                    "primary_trend": "未知",
                    "strength": "无法确定",
                    "support_levels": [],
                    "resistance_levels": []
                },
                "technical_signals": {
                    "moving_averages": "无数据",
                    "macd": "无数据", 
                    "rsi": {"value": 0, "interpretation": "无数据"},
                    "other_indicators": {}
                },
                "patterns": [],
                "trading_recommendation": {
                    "action": "持有",
                    "time_frame": "短期",
                    "reasoning": "缺少技术数据，无法进行分析"
                }
            }
            state.update_agent_output("TAA", empty_analysis)
            return state
        
        # 格式化价格数据
        formatted_price_data = self._format_price_data({ticker: price_data})
        
        # 构建提示
        prompt = self._format_prompt(
            self.analysis_template,
            price_data=formatted_price_data
        )
        
        # 获取系统消息
        system_message = self._get_system_message()
        
        # 调用模型获取结构化输出
        response = self.model_interface.get_structured_completion(
            prompt=prompt,
            system_message=system_message,
            output_format=self.output_format
        )
        
        # 记录交互
        self.log_interaction(prompt, json.dumps(response), {"ticker": ticker})
        
        # 更新状态中的TAA输出
        state.update_agent_output("TAA", response)
        
        return state
        
    def reflect(self, reward: float, state: LangGraphState) -> Dict[str, Any]:
        """
        基于奖励和状态进行反思
        
        参数:
            reward: 奖励值
            state: 当前状态
            
        返回:
            反思结果
        """
        # 构建反思提示
        reflection_template = """
As a Technical Analyst Agent, please reflect on your recent performance.

You received a reward value of: {reward}

Consider the following questions:
1. Was your trend analysis accurate?
2. Were the technical indicator signals you identified effective?
3. Did your trading recommendations lead to positive outcomes?
4. How can you improve your technical analysis methodology?

Please provide detailed reflections and a plan for improvement.
"""
        
        prompt = self._format_prompt(
            reflection_template,
            reward=reward
        )
        
        system_message = "你是一个能够进行自我反思和改进的技术分析智能体。"
        
        # 调用模型获取反思结果
        reflection = self.model_interface.get_completion(
            prompt=prompt,
            system_message=system_message
        )
        
        # 记录反思
        reflection_data = {
            "reward": reward,
            "reflection": reflection
        }
        
        # 添加到记忆
        self.add_to_memory(reflection_data)
        
        return {"reflection": reflection}
        
    def _format_price_data(self, price_history: Dict[str, List[Dict[str, Any]]]) -> str:
        """
        格式化价格数据
        
        参数:
            price_history: 价格历史数据
            
        返回:
            格式化的价格数据文本
        """
        formatted_data = []
        
        for stock, prices in price_history.items():
            formatted_data.append(f"股票: {stock}")
            formatted_data.append("日期\t\t开盘价\t收盘价\t最高价\t最低价\t成交量\tSMA_20\tSMA_50\tMACD\tSignal\tRSI")
            formatted_data.append("-" * 100)
            
            # 按日期排序（从旧到新）
            sorted_prices = sorted(prices, key=lambda x: x.get('date', ''))
            
            for price_point in sorted_prices:
                date = price_point.get('date', 'N/A')
                open_price = price_point.get('open', 'N/A')
                close = price_point.get('close', 'N/A')
                high = price_point.get('high', 'N/A')
                low = price_point.get('low', 'N/A')
                volume = price_point.get('volume', 'N/A')
                
                # 技术指标
                sma_20 = price_point.get('SMA_20', 'N/A')
                sma_50 = price_point.get('SMA_50', 'N/A')
                macd = price_point.get('MACD', 'N/A')
                signal = price_point.get('Signal_Line', 'N/A')
                rsi = price_point.get('RSI', 'N/A')
                
                line = f"{date}\t{open_price}\t{close}\t{high}\t{low}\t{volume}\t{sma_20}\t{sma_50}\t{macd}\t{signal}\t{rsi}"
                formatted_data.append(line)
                
            formatted_data.append("\n")
            
        if not formatted_data:
            return "没有可用的价格数据。"
            
        return "\n".join(formatted_data) 