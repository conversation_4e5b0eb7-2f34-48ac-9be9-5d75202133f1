"""
基本面分析智能体

分析公司财务数据、业绩报告和行业趋势，提供基本面分析
"""
import json
from typing import Dict, Any, List, Optional
from .base_agent import BaseAgent
from utils.langgraph_state import LangGraphState

class FundamentalAnalystAgent(BaseAgent):
    """
    基本面分析智能体
    
    分析公司财务数据、业绩报告和行业趋势，提供基本面分析
    """
    
    def __init__(self, agent_id: str, agent_type: str, model_interface=None):
        """
        初始化基本面分析智能体
        
        参数:
            agent_id: 智能体ID
            agent_type: 智能体类型
            model_interface: 模型接口
        """
        super().__init__(agent_id, agent_type, model_interface)
        
        # 设置默认提示参数
        self.prompt_params = {
            "valuation_focus": "Focus on valuation metrics like P/E, P/B, P/S compared to industry averages.",
            "financial_health_metrics": "Prioritize analysis of balance sheet health, cash flow, and solvency ratios.",
            "growth_analysis": "Analyze revenue and earnings growth trends and sustainability."
        }
        
        # 基本面分析模板
        self.analysis_template = """
You are a professional equity fundamental analyst specializing in US stocks. Analyze the following company's financial data and fundamental information, and provide in-depth insights.

{valuation_focus}
{financial_health_metrics}
{growth_analysis}

Here is the fundamental data to analyze:
{fundamental_data}

Please provide your analysis in the following structured format:
1. Valuation Analysis (P/E, P/B, P/S ratios, etc., including industry averages if available)
2. Financial Health (Balance Sheet, Cash Flow, Solvency)
3. Growth Prospects (Revenue Growth, Earnings Growth, Market Share)
4. Competitive Advantages and Risks
5. Investment Recommendation (Long-term investment value assessment based on fundamentals)

Ensure your analysis is concise, professional, and insightful.
"""

        # 结构化输出格式
        self.output_format = {
            "valuation_analysis": {
                "pe_ratio": {
                    "value": 0,
                    "industry_avg": 0,
                    "assessment": ""  # 低估/合理/高估
                },
                "pb_ratio": {
                    "value": 0,
                    "industry_avg": 0,
                    "assessment": ""
                },
                "ps_ratio": {
                    "value": 0,
                    "industry_avg": 0,
                    "assessment": ""
                },
                "overall_valuation": ""  # 低估/合理/高估
            },
            "financial_health": {
                "debt_to_equity": 0,
                "current_ratio": 0,
                "quick_ratio": 0,
                "interest_coverage": 0,
                "cash_flow_quality": "",  # 强/中/弱
                "overall_health": ""  # 强/中/弱
            },
            "growth_prospects": {
                "revenue_growth": {
                    "yoy_rate": 0,  # 同比增长率
                    "forecast": 0,  # 预测增长率
                    "assessment": ""  # 强劲/稳定/疲软
                },
                "earnings_growth": {
                    "yoy_rate": 0,
                    "forecast": 0,
                    "assessment": ""
                },
                "market_share_trend": "",
                "overall_growth_outlook": ""  # 积极/中性/消极
            },
            "competitive_analysis": {
                "strengths": [],
                "weaknesses": [],
                "opportunities": [],
                "threats": [],
                "moat_assessment": ""  # 宽/中/窄/无
            },
            "investment_recommendation": {
                "rating": "",  # 买入/持有/卖出
                "time_horizon": "",  # 短期/中期/长期
                "target_price": 0,
                "reasoning": ""
            }
        }
        
    def process(self, state: LangGraphState) -> LangGraphState:
        """
        处理基本面数据并生成分析报告
        
        参数:
            state: 包含基本面数据的LangGraphState
            
        返回:
            更新后的LangGraphState
        """
        # 提取基本面数据
        fundamental_data = state.fundamental_data
        
        # 如果没有基本面数据，返回空分析
        if not fundamental_data:
            empty_analysis = {
                "valuation_analysis": {
                    "pe_ratio": {"value": 0, "industry_avg": 0, "assessment": "无数据"},
                    "pb_ratio": {"value": 0, "industry_avg": 0, "assessment": "无数据"},
                    "ps_ratio": {"value": 0, "industry_avg": 0, "assessment": "无数据"},
                    "overall_valuation": "无法评估"
                },
                "financial_health": {
                    "debt_to_equity": 0, "current_ratio": 0, "quick_ratio": 0,
                    "interest_coverage": 0, "cash_flow_quality": "无数据",
                    "overall_health": "无法评估"
                },
                "growth_prospects": {
                    "revenue_growth": {"yoy_rate": 0, "forecast": 0, "assessment": "无数据"},
                    "earnings_growth": {"yoy_rate": 0, "forecast": 0, "assessment": "无数据"},
                    "market_share_trend": "无数据", "overall_growth_outlook": "无法评估"
                },
                "competitive_analysis": {
                    "strengths": [], "weaknesses": [], "opportunities": [], "threats": [],
                    "moat_assessment": "无法评估"
                },
                "investment_recommendation": {
                    "rating": "持有", "time_horizon": "短期", "target_price": 0,
                    "reasoning": "缺少基本面数据，无法进行分析"
                }
            }
            state.update_agent_output("FAA", empty_analysis)
            return state
        
        # 格式化基本面数据
        formatted_fundamental_data = self._format_fundamental_data(fundamental_data)
        
        # 构建提示
        prompt = self._format_prompt(
            self.analysis_template,
            fundamental_data=formatted_fundamental_data
        )
        
        # 获取系统消息
        system_message = self._get_system_message()
        
        # 调用模型获取结构化输出
        response = self.model_interface.get_structured_completion(
            prompt=prompt,
            system_message=system_message,
            output_format=self.output_format
        )
        
        # 记录交互
        self.log_interaction(prompt, json.dumps(response), {"ticker": state.ticker})
        
        # 更新状态中的FAA输出
        state.update_agent_output("FAA", response)
        
        return state
        
    def reflect(self, reward: float, state: LangGraphState) -> Dict[str, Any]:
        """
        基于奖励和状态进行反思
        
        参数:
            reward: 奖励值
            state: 当前状态
            
        返回:
            反思结果
        """
        # 构建反思提示
        reflection_template = """
As a Fundamental Analyst Agent, please reflect on your recent performance.

You received a reward value of: {reward}

Consider the following questions:
1. Was your valuation analysis accurate?
2. Was your assessment of the company's financial health reasonable?
3. Did your growth prospects forecast align with actual results?
4. Did your investment recommendations lead to positive outcomes?
5. How can you improve your fundamental analysis methodology?

Please provide detailed reflections and a plan for improvement.
"""
        
        prompt = self._format_prompt(
            reflection_template,
            reward=reward
        )
        
        system_message = "你是一个能够进行自我反思和改进的基本面分析智能体。"
        
        # 调用模型获取反思结果
        reflection = self.model_interface.get_completion(
            prompt=prompt,
            system_message=system_message
        )
        
        # 记录反思
        reflection_data = {
            "reward": reward,
            "reflection": reflection
        }
        
        # 添加到记忆
        self.add_to_memory(reflection_data)
        
        return {"reflection": reflection}
        
    def _format_fundamental_data(self, fundamental_data: Dict[str, Dict[str, Any]]) -> str:
        """
        格式化基本面数据
        
        参数:
            fundamental_data: 基本面数据
            
        返回:
            格式化的基本面数据文本
        """
        formatted_data = []
        
        for stock, data in fundamental_data.items():
            formatted_data.append(f"股票: {stock}")
            formatted_data.append("-" * 50)
            
            # 基本信息
            formatted_data.append("基本信息:")
            if "company_name" in data:
                formatted_data.append(f"公司名称: {data.get('company_name', 'N/A')}")
            if "sector" in data:
                formatted_data.append(f"行业: {data.get('sector', 'N/A')}")
            if "industry" in data:
                formatted_data.append(f"细分行业: {data.get('industry', 'N/A')}")
            
            # 估值指标
            formatted_data.append("\n估值指标:")
            if "pe_ratio" in data:
                formatted_data.append(f"市盈率 (P/E): {data.get('pe_ratio', 'N/A')}")
            if "pb_ratio" in data:
                formatted_data.append(f"市净率 (P/B): {data.get('pb_ratio', 'N/A')}")
            if "ps_ratio" in data:
                formatted_data.append(f"市销率 (P/S): {data.get('ps_ratio', 'N/A')}")
            if "dividend_yield" in data:
                formatted_data.append(f"股息收益率: {data.get('dividend_yield', 'N/A')}")
            
            # 财务数据
            formatted_data.append("\n财务数据:")
            if "revenue" in data:
                formatted_data.append(f"营收: {data.get('revenue', 'N/A')}")
            if "eps" in data:
                formatted_data.append(f"每股收益 (EPS): {data.get('eps', 'N/A')}")
            if "profit_margin" in data:
                formatted_data.append(f"利润率: {data.get('profit_margin', 'N/A')}")
            if "debt_to_equity" in data:
                formatted_data.append(f"负债权益比: {data.get('debt_to_equity', 'N/A')}")
            if "current_ratio" in data:
                formatted_data.append(f"流动比率: {data.get('current_ratio', 'N/A')}")
            if "quick_ratio" in data:
                formatted_data.append(f"速动比率: {data.get('quick_ratio', 'N/A')}")
            
            # 增长数据
            formatted_data.append("\n增长数据:")
            if "revenue_growth" in data:
                formatted_data.append(f"营收增长率: {data.get('revenue_growth', 'N/A')}")
            if "eps_growth" in data:
                formatted_data.append(f"EPS增长率: {data.get('eps_growth', 'N/A')}")
            if "roe" in data:
                formatted_data.append(f"股本回报率 (ROE): {data.get('roe', 'N/A')}")
            if "roa" in data:
                formatted_data.append(f"资产回报率 (ROA): {data.get('roa', 'N/A')}")
            
            # 分析师预测
            if "analyst_recommendations" in data:
                formatted_data.append("\n分析师评级:")
                recommendations = data.get("analyst_recommendations", {})
                if isinstance(recommendations, dict):
                    for rating, count in recommendations.items():
                        formatted_data.append(f"{rating}: {count}")
            
            # 最近财报
            if "recent_earnings" in data:
                formatted_data.append("\n最近财报:")
                earnings = data.get("recent_earnings", {})
                if isinstance(earnings, dict):
                    for key, value in earnings.items():
                        formatted_data.append(f"{key}: {value}")
            
            formatted_data.append("\n" + "=" * 50 + "\n")
        
        if not formatted_data:
            return "没有可用的基本面数据。"
            
        return "\n".join(formatted_data) 