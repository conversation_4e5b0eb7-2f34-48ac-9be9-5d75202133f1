"""
看多展望智能体

负责分析市场数据并提供看多的市场展望和交易建议
"""
import os
import json
import datetime
from typing import Dict, List, Any, Optional, Union

from agents.base_agent import BaseAgent
from utils.langgraph_state import LangGraphState

class BullishOutlookAgent(BaseAgent):
    """
    看多展望智能体
    
    分析市场数据并提供看多的市场展望和交易建议
    """
    
    def __init__(self, agent_id: str, agent_type: str, model_interface=None):
        """
        初始化看多展望智能体
        
        参数:
            agent_id: 智能体ID
            agent_type: 智能体类型
            model_interface: 模型接口
        """
        super().__init__(agent_id, agent_type, model_interface)
        
        # 初始化提示参数
        self.prompt_params = {
            "confidence_threshold": 0.6,  # Bullish signal confidence threshold
            "risk_tolerance": 0.7,        # Risk tolerance (0-1)
            "time_horizon": "medium",     # Time horizon: short, medium, long
            "technical_weight": 0.4,      # Technical analysis weight
            "fundamental_weight": 0.3,    # Fundamental analysis weight
            "news_weight": 0.3,           # News analysis weight
            "use_structured_output": False,  # Whether to use structured output
        }
        
        # 定义核心分析模板
        self.analysis_template = """
You are a professional Bullish Outlook Agent, responsible for analyzing market data and providing a bullish market outlook and trading recommendations.
Current Date: {current_date}

Please analyze the following analysis reports and provide your outlook and recommendations from a bullish perspective.

News Analysis:
{news_analysis}

Technical Analysis:
{technical_analysis}

Fundamental Analysis:
{fundamental_analysis}

Please provide the following information:
1. Bullish Reasons: List the key factors supporting a bullish view.
2. Potential Upside Targets: Predict possible price targets for an increase.
3. Recommended Holding Period: Short-term, medium-term, or long-term.
4. Bullish Confidence Score: A value between 0-100, indicating the level of confidence in the bullish view.
5. Risk Factors: Factors that could negatively impact the bullish view.
6. Trading Recommendation: Specific trading advice, including entry points, stop loss, etc.

If your bullish confidence score is below {confidence_threshold_percentage}, please state so honestly and explain why.

Please respond in JSON format with the following fields:
{{
            "bullish_reasons": ["Reason 1", "Reason 2", ...],
            "price_targets": {{
                "conservative": "Price",
                "moderate": "Price",
                "aggressive": "Price"
            }},
            "recommended_holding_period": "short/medium/long",
            "confidence_score": "value between 0-100",
            "risk_factors": ["Risk 1", "Risk 2", ...],
            "trading_recommendation": {{
                "action": "Buy/Hold/Observe",
                "entry_points": ["Price 1", "Price 2", ...],
                "stop_loss": "Price",
                "take_profit": "Price"
            }},
            "summary": "Summarizing description"
}}
"""
        # 初始化 current_prompt_template，它现在会使用上面的 analysis_template
        self.current_prompt_template = self.get_default_prompt_template()

        # 结构化输出格式
        self.output_format = {
            "market_outlook": {
                "summary": "",
                "confidence": 0.0,
                "key_factors": [],
                "time_horizon": ""
            },
            "stock_outlooks": {},
            "trade_recommendations": {},
            "risk_assessment": {
                "overall_risk": 0.0,
                "key_risks": [],
                "hedging_suggestions": []
            }
        }
        
    def process(self, state: LangGraphState) -> LangGraphState:
        """
        处理当前状态，生成看多展望
        
        参数:
            state: LangGraphState当前状态
            
        返回:
            更新后的LangGraphState
        """
        # 获取初始分析层的结果
        naa_output = state.naa_output
        taa_output = state.taa_output
        faa_output = state.faa_output
        
        # 检查是否有必要的输入数据
        if not naa_output and not taa_output and not faa_output:
            empty_outlook = {
                "error": "缺少必要的输入数据",
                "bullish_reasons": [],
                "price_targets": {"conservative": "0", "moderate": "0", "aggressive": "0"},
                "recommended_holding_period": "short",
                "confidence_score": "0",
                "risk_factors": ["缺少分析数据"],
                "trading_recommendation": {
                    "action": "Observe",
                    "entry_points": [],
                    "stop_loss": "0",
                    "take_profit": "0"
                },
                "summary": "缺少必要的分析数据，无法提供看多展望"
            }
            state.update_agent_output("BOA", empty_outlook)
            return state
        
        # 构建提示
        current_date = state.current_date
        formatted_news_analysis = self._format_analysis(naa_output)
        formatted_technical_analysis = self._format_analysis(taa_output)
        formatted_fundamental_analysis = self._format_analysis(faa_output)
        confidence_threshold_percentage = self.prompt_params.get("confidence_threshold", 0.7) * 100

        prompt = self._format_prompt(
            self.current_prompt_template, # 使用可优化的模板
            current_date=current_date,
            news_analysis=formatted_news_analysis,
            technical_analysis=formatted_technical_analysis,
            fundamental_analysis=formatted_fundamental_analysis,
            confidence_threshold_percentage=confidence_threshold_percentage
        )
        
        # 获取系统消息
        system_message = self._get_system_message()
        
        # 根据配置决定是否使用结构化输出
        use_structured = self.prompt_params.get("use_structured_output", False)
        
        if use_structured:
            # 调用模型获取结构化输出
            response = self.model_interface.get_structured_completion(
                prompt=prompt,
                system_message=system_message,
                output_format=self.output_format
            )
            # 确保响应是可序列化的
            response = self._ensure_serializable(response)
        else:
            # 使用非结构化输出
            response_text = self.model_interface.get_completion(
                prompt=prompt,
                system_message=system_message
            )
            # 将文本响应封装为字典
            response = {
                "response": response_text,
                "market_outlook": self._extract_market_outlook(response_text),
                "trade_recommendations": self._extract_trade_recommendations(response_text)
            }
        
        # 记录交互
        self.log_interaction(prompt, json.dumps(response, ensure_ascii=False), {"ticker": state.ticker})
        
        # 添加元数据
        response["agent_id"] = self.agent_id
        response["agent_type"] = self.agent_type
        
        # 更新状态中的BOA输出
        state.update_agent_output("BOA", response)
        
        return state
        
    def _validate_input(self, state: Dict[str, Any]) -> bool:
        """验证输入数据的完整性"""
        # 修改为接受初始分析层的输出
        required_keys = ["naa_output", "taa_output", "faa_output"]
        return all(key in state for key in required_keys)
        
    def _build_prompt(self, state: Dict[str, Any], short_term_analysis: Dict[str, Any], long_term_analysis: Dict[str, Any], confidence_threshold: float = 0.7) -> str:
        """
        构建提示 (此方法现在主要用于获取模板，实际格式化和历史记录由 _format_prompt 处理)
        
        参数:
            state: 当前状态
            short_term_analysis: 短期分析结果
            long_term_analysis: 长期分析结果
            confidence_threshold: 置信度阈值，默认为0.7
            
        返回:
            提示模板字符串 (不再是最终格式化的提示)
        """
        return self.analysis_template

    def _ensure_serializable(self, data: Any) -> Any:
        """
        确保数据是JSON可序列化的
        
        参数:
            data: 输入数据
            
        返回:
            处理后的可序列化数据
        """
        if isinstance(data, dict):
            result = {}
            for k, v in data.items():
                # 处理Timestamp键
                if hasattr(k, 'isoformat') or str(type(k)).find("pandas") >= 0:
                    k = str(k)  # 将Timestamp键转换为字符串
                result[k] = self._ensure_serializable(v)
            return result
        elif isinstance(data, list):
            return [self._ensure_serializable(item) for item in data]
        elif hasattr(data, 'items') and callable(data.items):  # 处理类字典对象(如Series)
            return {str(k): self._ensure_serializable(v) for k, v in data.items()}
        elif hasattr(data, 'isoformat'):  # 处理日期时间类型
            return data.isoformat()
        # 专门处理pandas的Timestamp类型
        elif str(type(data)).endswith("pandas._libs.tslibs.timestamps.Timestamp'>"):
            return data.isoformat()
        elif str(type(data)).find("pandas") >= 0 and hasattr(data, 'to_dict'):
            # 对于其他pandas类型，尝试to_dict()后再确保可序列化
            try:
                dict_data = data.to_dict()
                return self._ensure_serializable(dict_data)
            except:
                return str(data)
        elif isinstance(data, (int, float, str, bool, type(None))):
            return data
        else:
            # 将不可序列化的对象转换为字符串
            return str(data)
            
    def _extract_market_outlook(self, text: str) -> Dict[str, Any]:
        """
        从非结构化文本中提取市场展望信息
        
        参数:
            text: 模型生成的文本响应
            
        返回:
            市场展望字典
        """
        # 简单实现，实际应用中可以使用更复杂的提取逻辑
        market_outlook = {
            "summary": "",
            "key_factors": []
        }
        
        # 查找市场展望部分
        if "## Market Outlook Summary" in text:
            sections = text.split("##")
            for section in sections:
                if "Market Outlook Summary" in section:
                    lines = section.strip().split("\n")[1:]
                    market_outlook["summary"] = "\n".join(lines).strip()
                elif "Key Bullish Factors" in section:
                    lines = section.strip().split("\n")[1:]
                    for line in lines:
                        if line.strip() and line.strip()[0].isdigit():
                            # 移除数字前缀并添加到因素列表
                            factor = line.strip()
                            for prefix in ["1.", "2.", "3.", "4.", "5.", "- "]:
                                if factor.startswith(prefix):
                                    factor = factor[len(prefix):].strip()
                                    break
                            if factor:
                                market_outlook["key_factors"].append(factor)
        
        return market_outlook
        
    def _extract_trade_recommendations(self, text: str) -> Dict[str, Any]:
        """
        从非结构化文本中提取交易建议
        
        参数:
            text: 模型生成的文本响应
            
        返回:
            交易建议字典
        """
        recommendations = {}
        
        # 查找交易建议部分
        if "## Trading Recommendation" in text:
            sections = text.split("##")
            for section in sections:
                if "Trading Recommendation" in section:
                    lines = section.strip().split("\n")[1:]
                    for line in lines:
                        if line.strip() and line.strip().startswith("- "):
                            # 解析交易建议行
                            parts = line.strip()[2:].split(":")
                            if len(parts) >= 2:
                                symbol = parts[0].strip()
                                action = parts[1].strip()
                                recommendations[symbol] = action
                            
        return recommendations
        
    def reflect(self, reward: float, state: LangGraphState) -> Dict[str, Any]:
        """
        基于奖励进行反思
        
        参数:
            reward: 获得的奖励
            state: 当前状态
            
        返回:
            反思结果
        """
        # 构建反思提示
        prompt = f"""
You are a professional Bullish Outlook Agent reflecting on your performance.

Your previous bullish outlook received a reward of: {reward:.4f}

Please reflect on the following points:
1. Was your bullish outlook accurate? Why or why not?
2. Did you miss any important bullish signals?
3. Did you overinterpret any bullish signals?
4. How can you improve your bullish outlook analysis?
"""
        
        # 获取系统消息
        system_message = "You are a Bullish Outlook Agent capable of self-reflection and improvement."
        
        # 根据配置决定是否使用结构化输出
        use_structured = self.prompt_params.get("use_structured_output", False)
        
        # 定义反思输出格式
        reflection_format = {
            "accuracy_assessment": "",
            "missed_signals": [],
            "overinterpreted_signals": [],
            "improvement_plans": [],
            "parameter_adjustments": {
                "confidence_threshold": 0.0,
                "risk_tolerance": 0.0,
                "technical_weight": 0.0,
                "fundamental_weight": 0.0,
                "news_weight": 0.0
            }
        }
        
        if use_structured:
            # 结构化反思格式提示
            prompt += f"""
Please provide the following JSON output format:
```json
{json.dumps(reflection_format, indent=2, ensure_ascii=False)}
```
"""
            
            # 调用模型获取结构化反思
            reflection = self.model_interface.get_structured_completion(
                prompt=prompt,
                system_message=system_message,
                output_format=reflection_format
            )
        else:
            # 非结构化反思格式提示
            prompt += """
Please structure your reflection as follows (plain text format):

## Accuracy Assessment
[Assess the accuracy of your forecast]

## Missed Signals
1. [Signal 1]
2. [Signal 2]
...

## Overinterpreted Signals
1. [Signal 1]
2. [Signal 2]
...

## Improvement Plans
1. [Plan 1]
2. [Plan 2]
...

## Parameter Adjustment Suggestions
- Confidence Threshold for Bullish Signals: [0.0-1.0]
- Risk Tolerance: [0.0-1.0]
- Technical Analysis Weight: [0.0-1.0]
- Fundamental Analysis Weight: [0.0-1.0]
- News Analysis Weight: [0.0-1.0]
"""
            
            # 获取非结构化反思文本
            reflection_text = self.model_interface.get_completion(
                prompt=prompt,
                system_message=system_message
            )
            
            # 将文本转换为结构化格式
            reflection = self._parse_reflection_text(reflection_text)
        
        # 更新参数（如果有建议的话）
        if "parameter_adjustments" in reflection:
            for param, value in reflection["parameter_adjustments"].items():
                if param in self.prompt_params and value > 0:
                    self.prompt_params[param] = value
        
        # 记录交互
        self.log_interaction(prompt, json.dumps(reflection, ensure_ascii=False), {"reward": reward})
        
        return reflection
        
    def _parse_reflection_text(self, text: str) -> Dict[str, Any]:
        """
        解析非结构化反思文本
        
        参数:
            text: 反思文本
            
        返回:
            结构化的反思字典
        """
        reflection = {
            "accuracy_assessment": "",
            "missed_signals": [],
            "overinterpreted_signals": [],
            "improvement_plans": [],
            "parameter_adjustments": {}
        }
        
        # 解析各部分
        sections = text.split("##")
        for section in sections:
            section = section.strip()
            if not section:
                continue
                
            lines = section.split("\n")
            section_title = lines[0].strip()
            content = "\n".join(lines[1:]).strip()
            
            if "Accuracy Assessment" in section_title:
                reflection["accuracy_assessment"] = content
            elif "Missed Signals" in section_title:
                for line in content.split("\n"):
                    line = line.strip()
                    if line and (line[0].isdigit() or line.startswith("- ")):
                        # 清理前缀
                        for prefix in ["1.", "2.", "3.", "4.", "5.", "- "]:
                            if line.startswith(prefix):
                                line = line[len(prefix):].strip()
                                break
                        reflection["missed_signals"].append(line)
            elif "Overinterpreted Signals" in section_title:
                for line in content.split("\n"):
                    line = line.strip()
                    if line and (line[0].isdigit() or line.startswith("- ")):
                        # 清理前缀
                        for prefix in ["1.", "2.", "3.", "4.", "5.", "- "]:
                            if line.startswith(prefix):
                                line = line[len(prefix):].strip()
                                break
                        reflection["overinterpreted_signals"].append(line)
            elif "Improvement Plans" in section_title:
                for line in content.split("\n"):
                    line = line.strip()
                    if line and (line[0].isdigit() or line.startswith("- ")):
                        # 清理前缀
                        for prefix in ["1.", "2.", "3.", "4.", "5.", "- "]:
                            if line.startswith(prefix):
                                line = line[len(prefix):].strip()
                                break
                        reflection["improvement_plans"].append(line)
            elif "Parameter Adjustment Suggestions" in section_title:
                for line in content.split("\n"):
                    line = line.strip()
                    if ":" in line:
                        param, value_str = line.split(":", 1)
                        param = param.strip().replace("- ", "")
                        
                        # 提取参数名称
                        param_mapping = {
                            "Confidence Threshold for Bullish Signals": "confidence_threshold",
                            "Risk Tolerance": "risk_tolerance",
                            "Technical Analysis Weight": "technical_weight",
                            "Fundamental Analysis Weight": "fundamental_weight",
                            "News Analysis Weight": "news_weight"
                        }
                        
                        param_key = param_mapping.get(param)
                        if param_key:
                            try:
                                # 提取数值
                                value = float(value_str.strip())
                                reflection["parameter_adjustments"][param_key] = value
                            except:
                                pass
        
        return reflection
        
    def _format_analysis(self, analysis_data: Union[Dict[str, Any], str]) -> str:
        """
        格式化分析数据
        
        参数:
            analysis_data: 分析数据，可能是字典或字符串
            
        返回:
            格式化的分析数据文本
        """
        # 如果analysis_data是字符串，直接返回
        if isinstance(analysis_data, str):
            return analysis_data
            
        # 如果analysis_data为空
        if not analysis_data:
            return "没有可用的分析数据。"
            
        # 如果是字典，尝试提取有用信息
        if isinstance(analysis_data, dict):
            # 检查是否有response字段
            if "response" in analysis_data:
                return analysis_data["response"]
            
            # 尝试提取market_sentiment字段
            if "market_sentiment" in analysis_data:
                return f"Market Sentiment: {analysis_data['market_sentiment']}"
            
            # 如果有analysis字段
            if "analysis" in analysis_data:
                return analysis_data["analysis"]
                
            # 如果有summary字段
            if "summary" in analysis_data:
                return analysis_data["summary"]
                
            # 如果没有特定字段，转换为JSON字符串
            return json.dumps(analysis_data, indent=2, ensure_ascii=False)
            
        # 其他情况
        return str(analysis_data) 