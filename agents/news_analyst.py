"""
新闻分析智能体

分析新闻数据，提供市场情绪和事件影响分析
"""
import json
from typing import Dict, Any, List, Optional
from .base_agent import BaseAgent
from utils.langgraph_state import LangGraphState

class NewsAnalystAgent(BaseAgent):
    """
    新闻分析智能体
    
    分析新闻数据，提供市场情绪和事件影响分析
    """
    
    def __init__(self, agent_id: str, agent_type: str, model_interface=None):
        """
        初始化新闻分析智能体
        
        参数:
            agent_id: 智能体ID
            agent_type: 智能体类型
            model_interface: 模型接口
        """
        super().__init__(agent_id, agent_type, model_interface)
        
        # 设置默认提示参数
        self.prompt_params = {
            "sentiment_instructions": "Pay close attention to quantifying sentiment analysis.",
            "continuity_instructions": "Focus on the continuity of news and potential trend shifts.",
            "impact_instructions": "Analyze the short-term and long-term market impact of news."
        }
        
        # 设置输出格式
        self.output_format = {
            "key_events": [],
            "market_sentiment_score": 0,
            "ticker_specific_impact": "",
            "key_investment_factors": [],
            "confidence_rating": 0
        }
        
        # 新闻分析模板
        self.analysis_template = """
You are a professional US stock market news analyst. Analyze the following news data regarding {ticker} and provide market sentiment and event impact analysis.

Analysis Requirements:
1. Identify key news events and prioritize by significance.
2. Evaluate the sentiment of each news piece (positive, neutral, or negative) {sentiment_instructions}.
3. Pay special attention to news directly related to {ticker}.
4. Analyze the continuity of news compared to previous trading days {continuity_instructions}.
5. Consider the credibility and market influence of the news {impact_instructions}.

Here is the news data to analyze:
{news_data}

Please provide your analysis in the following structured format:
1. Key Event Summary (maximum 5 items, prioritized by importance)
2. Overall Market Sentiment Assessment (score 1-10, where 1 is extremely negative and 10 is extremely positive)
3. Ticker-Specific Impact Analysis for {ticker}
4. Most Important Factors Likely to Influence Investment Decisions (maximum 3 items)
5. Confidence Rating (1-5 stars, 5 stars indicating high confidence)

Ensure your analysis is concise, professional, and insightful.
"""
        
        # 设置current_prompt_template
        self.current_prompt_template = self.analysis_template
        
    def _format_news_data(self, news_data: List[Dict[str, Any]]) -> str:
        """
        格式化新闻数据
        
        参数:
            news_data: 新闻数据列表
            
        返回:
            格式化后的新闻数据字符串
        """
        formatted_news = []
        
        for i, news in enumerate(news_data[:20]):  # 限制最多处理20条新闻
            title = news.get("title", "无标题")
            time = news.get("time_published", "未知时间")
            summary = news.get("summary", "无摘要")
            source = news.get("source", "未知来源")
            
            formatted_news.append(f"新闻 #{i+1}:")
            formatted_news.append(f"标题: {title}")
            formatted_news.append(f"时间: {time}")
            formatted_news.append(f"来源: {source}")
            formatted_news.append(f"摘要: {summary}")
            formatted_news.append("")
        
        return "\n".join(formatted_news)
        
    def process(self, state: LangGraphState) -> LangGraphState:
        """
        处理新闻数据并生成分析报告
        
        参数:
            state: 包含新闻数据的LangGraphState
            
        返回:
            更新后的LangGraphState
        """
        # 提取新闻数据和股票代码
        news_data = state.news_data
        ticker = state.ticker
        
        # 如果没有新闻数据，返回空分析
        if not news_data:
            empty_analysis = {
                "key_events": ["无新闻数据可分析"],
                "market_sentiment_score": 5,
                "ticker_specific_impact": "无法分析，缺少新闻数据",
                "key_investment_factors": ["数据不足"],
                "confidence_rating": 1
            }
            state.update_agent_output("NAA", empty_analysis)
            return state
        
        # 格式化新闻数据
        formatted_news_data = self._format_news_data(news_data)
        
        # 构建提示
        prompt = self._format_prompt(
            self.analysis_template,
            ticker=ticker,
            news_data=formatted_news_data
        )
        
        # 获取系统消息
        system_message = self._get_system_message()
        
        # 调用模型获取结构化输出
        response = self.model_interface.get_structured_completion(
            prompt=prompt,
            system_message=system_message,
            output_format=self.output_format
        )
        
        # 记录交互
        self.log_interaction(prompt, json.dumps(response), {"ticker": ticker, "news_count": len(news_data)})
        
        # 更新状态中的NAA输出
        state.update_agent_output("NAA", response)
        
        return state
        
    def reflect(self, reward: float, state: LangGraphState) -> Dict[str, Any]:
        """
        基于奖励和状态进行反思
        
        参数:
            reward: 奖励值
            state: 当前状态
            
        返回:
            反思结果
        """
        # 构建反思提示
        reflection_template = """
As a News Analyst Agent, please reflect on your recent performance.

You received a reward value of: {reward}

Consider the following questions:
1. Did you correctly identify key news events?
2. Was your market sentiment assessment accurate?
3. Was your analysis of ticker-specific impacts insightful?
4. Did you correctly identify the key factors for investment decisions?
5. How can you improve your news analysis methodology?

Please provide detailed reflections and a plan for improvement.
"""
        
        prompt = self._format_prompt(
            reflection_template,
            reward=reward
        )
        
        system_message = "你是一个能够进行自我反思和改进的新闻分析智能体。"
        
        # 调用模型获取反思结果
        reflection = self.model_interface.get_completion(
            prompt=prompt,
            system_message=system_message
        )
        
        # 记录反思
        reflection_data = {
            "reward": reward,
            "reflection": reflection
        }
        
        # 添加到记忆
        self.add_to_memory(reflection_data)
        
        return {"reflection": reflection} 