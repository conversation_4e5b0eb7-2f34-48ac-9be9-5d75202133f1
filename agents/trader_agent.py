"""
交易员智能体

负责根据各种展望智能体的建议，做出最终交易决策
"""
import os
import json
from typing import Dict, List, Any, Optional, Union

from agents.base_agent import BaseAgent
from utils.langgraph_state import LangGraphState

class TraderAgent(BaseAgent):
    """
    交易员智能体
    
    根据各种展望智能体的建议，做出最终交易决策
    """
    
    def __init__(self, agent_id: str, agent_type: str, model_interface=None):
        """
        初始化交易员智能体
        
        参数:
            agent_id: 智能体ID
            agent_type: 智能体类型
            model_interface: 模型接口
        """
        super().__init__(agent_id, agent_type, model_interface)
        
        # 初始化提示参数
        self.prompt_params = {
            "risk_tolerance": 0.6,        # Risk tolerance (0-1)
            "min_confidence": 0.65,       # Minimum confidence requirement for outlooks
            "boa_weight": 0.3,            # Bullish outlook weight
            "beoa_weight": 0.3,           # Bearish outlook weight
            "noa_weight": 0.4,            # Neutral outlook weight
        }
        
        # 定义核心分析模板
        self.analysis_template = """
You are a professional Trader Agent, responsible for making final trading decisions based on recommendations from various outlook agents.
Current Date: {current_date}

Based on the following information, make your final trading decision:

1. Bullish Outlook:
{bullish_outlook}

2. Bearish Outlook:
{bearish_outlook}

3. Neutral Outlook:
{neutral_outlook}

4. Current Positions:
{positions}

5. Current Cash: ${cash:.2f}

Risk Tolerance: {risk_tolerance} (Between 0-1, where 0 is extremely conservative and 1 is extremely aggressive)

IMPORTANT: You MUST choose ONE of the following two actions - HOLD is NOT allowed:
- BUY: Use ALL available cash to buy the stock (full position). Choose this if you believe the stock will go up.
- SELL: Sell ALL current stock holdings (liquidate all positions). Choose this if you believe the stock will go down.

You CANNOT choose HOLD. You must make a decisive trading decision based on the available information.

DECISION LOGIC:
1. If you have STRONG bullish signals (high confidence), choose BUY with the recommended ticker.
2. If you have STRONG bearish signals (high confidence), choose SELL.
3. If signals are mixed or weak:
   - If you currently have NO positions (all cash): Choose BUY with AAPL (default ticker) to enter the market
   - If you currently have positions: Choose SELL to take profits/cut losses
4. When choosing BUY, you MUST specify a ticker (use AAPL if no specific recommendation)

Your goal is to be ACTIVE in trading, not passive. Make decisive moves based on the best available information.

Please respond in JSON format with the following fields:
{{
            "market_assessment": "Overall market assessment",
            "final_decision": {{
                "action": "BUY/SELL",
                "ticker": "Stock ticker if BUY action, null if SELL",
                "reasoning": "Detailed reason for this decision"
            }},
            "confidence_level": "High/Medium/Low confidence in this decision",
            "summary": "Brief decision summary"
}}
"""
        # 初始化 current_prompt_template，它现在会使用上面的 analysis_template
        self.current_prompt_template = self.get_default_prompt_template()
        
    def process(self, state: LangGraphState) -> LangGraphState:
        """
        处理当前状态，生成交易决策
        
        参数:
            state: 当前状态
            
        返回:
            交易决策结果
        """
        # 获取各展望智能体的输出
        boa_output = state.boa_output
        beoa_output = state.beoa_output
        noa_output = state.noa_output
        
        # 检查是否有必要的输入数据
        if not boa_output and not beoa_output and not noa_output:
            empty_decision = {
                "error": "Missing required outlook data",
                "market_assessment": "无法评估，缺少展望数据",
                "final_decision": {
                    "action": "SELL",
                    "ticker": None,
                    "reasoning": "缺少必要的展望数据，选择保守策略"
                },
                "confidence_level": "Low",
                "summary": "缺少展望数据，无法做出有效决策",
                "actions": {"__SELL_ALL__": -1.0}
            }
            state.update_agent_output("TRA", empty_decision)
            return state
        
        # 构建提示
        current_date = state.current_date
        positions = state.positions
        cash = state.cash
        
        formatted_bullish = self._format_outlook(boa_output, "bullish")
        formatted_bearish = self._format_outlook(beoa_output, "bearish")
        formatted_neutral = self._format_outlook(noa_output, "neutral")
        formatted_positions = json.dumps(positions, indent=2, ensure_ascii=False)
        risk_tolerance = self.prompt_params.get("risk_tolerance", 0.5)

        prompt = self._format_prompt(
            self.current_prompt_template, # 使用可优化的模板
            current_date=current_date,
            bullish_outlook=formatted_bullish,
            bearish_outlook=formatted_bearish,
            neutral_outlook=formatted_neutral,
            positions=formatted_positions,
            cash=cash,
            risk_tolerance=risk_tolerance
        )
        
        # 获取系统消息
        system_message = self._get_system_message()
        
        # 调用模型
        response = self.model_interface.get_completion(prompt, system_message)
        
        # 解析模型输出
        output = self._parse_output(response)
        
        # 更新状态中的TRA输出
        state.update_agent_output("TRA", output)
        
        return state
        
    def _validate_input(self, state: Dict[str, Any]) -> bool:
        """验证输入数据的完整性"""
        required_keys = ["boa_output", "beoa_output", "noa_output"]
        return all(key in state for key in required_keys)
        
    def _build_prompt(self, state: Dict[str, Any], bullish_outlook: Dict[str, Any], 
                     bearish_outlook: Dict[str, Any], neutral_outlook: Dict[str, Any],
                     risk_tolerance: float = 0.5) -> str:
        """
        构建提示 (此方法现在主要用于获取模板，实际格式化和历史记录由 _format_prompt 处理)
        
        参数:
            state: 当前状态
            bullish_outlook: 看多展望
            bearish_outlook: 看空展望
            neutral_outlook: 中立展望
            risk_tolerance: 风险承受能力，默认为0.5
            
        返回:
            提示模板字符串 (不再是最终格式化的提示)
        """
        # 此方法在新的设计下，如果 analysis_template 直接在 __init__ 中定义，
        # 且 get_default_prompt_template 能正确获取它，则可能不再直接被 process 调用以构建完整提示。
        # 但如果 get_default_prompt_template 依赖它来构造模板，则它应该返回模板字符串。
        # 为保持兼容性和清晰性，暂时让它返回模板，但核心格式化已移走。
        return self.analysis_template
        
    def _format_outlook(self, outlook_data: Union[Dict[str, Any], str], outlook_type: str) -> str:
        """
        格式化展望数据
        
        参数:
            outlook_data: 展望数据，可能是字典或字符串
            outlook_type: 展望类型（bullish/bearish/neutral）
            
        返回:
            格式化的展望数据文本
        """
        # 如果outlook_data是字符串，直接返回
        if isinstance(outlook_data, str):
            return outlook_data
            
        # 如果outlook_data为空
        if not outlook_data:
            return f"No available {outlook_type} outlook data."
            
        # 如果是字典，尝试提取有用信息
        if isinstance(outlook_data, dict):
            # 检查是否有response字段
            if "response" in outlook_data:
                return outlook_data["response"]
            
            # 检查是否有summary字段
            if "summary" in outlook_data:
                return outlook_data["summary"]
                
            # 如果没有特定字段，转换为JSON字符串
            return json.dumps(outlook_data, indent=2, ensure_ascii=False)
            
        # 其他情况
        return str(outlook_data)
        
    def _parse_output(self, response: str) -> Dict[str, Any]:
        """解析模型输出"""
        try:
            # 提取并清理JSON部分
            json_str = self._extract_json(response)
            
            # 尝试解析JSON
            try:
                output = json.loads(json_str)
            except json.JSONDecodeError as e:
                # 如果解析失败，尝试进一步清理和修复
                # 修复常见问题
                # 1. 移除尾随逗号
                json_str = json_str.replace(",\n}", "\n}")
                json_str = json_str.replace(",\n]", "\n]")
                
                # 2. 确保属性名有引号
                import re
                json_str = re.sub(r'([{,]\s*)([a-zA-Z0-9_]+)(\s*:)', r'\1"\2"\3', json_str)
                
                output = json.loads(json_str)
            
            # 添加元数据
            output["agent_id"] = self.agent_id
            output["agent_type"] = self.agent_type

            # --- 解析简化的交易决策到 actions 的逻辑 ---
            parsed_actions = {}
            final_decision = output.get("final_decision", {})
            
            if final_decision:
                action = final_decision.get("action", "").strip().upper()
                ticker = final_decision.get("ticker")
                
                # 处理ticker为None的情况
                if ticker is not None:
                    ticker = ticker.strip()
                else:
                    ticker = None
                
                if action == "BUY" and ticker:
                    # 全仓买入：使用1.0表示使用所有可用现金买入该股票
                    parsed_actions[ticker] = 1.0
                    print(f"  🛒 交易决策: 全仓买入 {ticker}")
                    
                elif action == "BUY" and not ticker:
                    # BUY但没有指定ticker，使用默认的AAPL
                    default_ticker = "AAPL"
                    parsed_actions[default_ticker] = 1.0
                    print(f"  🛒 交易决策: 全仓买入 {default_ticker} (默认股票)")
                    
                elif action == "SELL":
                    # 全仓卖出：对所有持仓股票设置-1.0表示全部卖出
                    # 注意：这里需要从state中获取当前持仓信息
                    # 由于_parse_output方法中无法直接获取state，我们使用特殊标记
                    parsed_actions["__SELL_ALL__"] = -1.0
                    print(f"  💰 交易决策: 全仓卖出所有持仓")
                    
                else:
                    # 如果没有识别到有效动作，默认选择SELL（保持现金）
                    print(f"  ⚠️ 未识别的交易动作: '{action}' 或缺少ticker信息，默认选择SELL")
                    parsed_actions["__SELL_ALL__"] = -1.0
            else:
                # 如果没有final_decision，默认选择SELL
                print(f"  ⚠️ 没有找到交易决策，默认选择SELL")
                parsed_actions["__SELL_ALL__"] = -1.0
            
            # 将解析出的 actions 添加到输出字典中
            output["actions"] = parsed_actions
            
            return output
        except Exception as e:
            print(f"  ❌ 解析输出时出错: {str(e)}")
            return {
                "error": f"Error parsing output: {str(e)}",
                "raw_output": response,
                "actions": {}  # 确保即使出错也有actions字段
            }
            
    def reflect(self, reward: float, state: LangGraphState) -> Dict[str, Any]:
        """
        基于奖励进行反思
        
        参数:
            reward: 获得的奖励
            state: 当前状态
            
        返回:
            反思结果
        """
        # 构建反思提示
        prompt = f"""
You are a professional Trader Agent reflecting on your performance.

Your previous trading decisions resulted in a reward of: {reward:.4f}

Please reflect on the following points:
1. Were your trading decisions effective? Why or why not?
2. Did you correctly weigh the recommendations from different outlooks?
3. Was your risk management strategy appropriate?
4. How can you improve your trading decisions?

Please provide the following JSON output:
```json
{{
          "decision_assessment": "Assessment of trading decision effectiveness",
          "weighting_assessment": "Assessment of outlook weighting",
          "risk_management_assessment": "Assessment of risk management",
          "improvement_plans": ["Improvement plan 1", "Improvement plan 2", "..."],
          "parameter_adjustments": {{
            "risk_tolerance": "0.0-1.0",
            "min_confidence": "0.0-1.0",
            "boa_weight": "0.0-1.0",
            "beoa_weight": "0.0-1.0",
            "noa_weight": "0.0-1.0"
          }}
}}
```
"""
        
        # 获取系统消息
        system_message = "You are a Trader Agent capable of self-reflection and improvement."
        
        # 调用模型
        response = self.model_interface.get_completion(prompt, system_message)
        
        try:
            # 提取JSON部分
            json_str = self._extract_json(response)
            reflection = json.loads(json_str)
            
            # 更新参数（如果有建议的话）
            if "parameter_adjustments" in reflection:
                for param, value in reflection["parameter_adjustments"].items():
                    if param in self.prompt_params:
                        self.prompt_params[param] = value
            
            return reflection
        except Exception as e:
            return {
                "error": f"Error during reflection: {str(e)}",
                "raw_output": response
            }
            
    def _extract_json(self, text: str) -> str:
        """从文本中提取JSON字符串并清理"""
        json_str = ""
        
        # 首先尝试提取代码块中的内容
        if "```json" in text:
            # 提取```json和```之间的内容
            start = text.find("```json") + 7
            end = text.find("```", start)
            if end > start:
                json_str = text[start:end].strip()
        elif "```" in text:
            # 提取```和```之间的内容
            start = text.find("```") + 3
            end = text.find("```", start)
            if end > start:
                json_str = text[start:end].strip()
        else:
            # 尝试查找JSON对象
            import re
            # 查找第一个 JSON 对象 {...}
            match = re.search(r'\{.*\}', text, re.DOTALL)
            if match:
                json_str = match.group(0)
            else:
                # 假设整个文本就是JSON
                json_str = text.strip()
        
        # 清理可能的注释行和多余的空白
        lines = json_str.split('\n')
        cleaned_lines = []
        
        for line in lines:
            # 移除注释
            comment_pos = line.find('//')
            if comment_pos >= 0:
                line = line[:comment_pos].strip()
            
            # 跳过空行
            if line.strip():
                cleaned_lines.append(line)
        
        cleaned_json = '\n'.join(cleaned_lines)
        
        # 如果清理后的JSON为空，返回原始文本
        if not cleaned_json.strip():
            return text.strip()
            
        return cleaned_json 