"""
LangGraph-based Multi-Agent Coordinator

Implements the multi-agent trading system using LangGraph's StateGraph for 
workflow orchestration, replacing the traditional procedural coordination
with a graph-based approach.
"""

import os
import json
import time
from typing import Dict, List, Any, Optional, Callable, Set
from datetime import datetime
import importlib
import pandas as pd
import numpy as np

from langgraph.graph import StateGraph, START, END
from utils.langgraph_state import LangGraphState
from config import AGENT_TYPES, AGENT_GRAPH, get_run_id
from agents.base_agent import BaseAgent
from stock_trading_env import StockTradingEnv
from utils.performance_metrics import calculate_sharpe_ratio


class LangGraphCoordinator:
    """
    LangGraph-based multi-agent coordinator
    
    Uses StateGraph to orchestrate agent execution in a directed graph workflow,
    providing better control flow, parallel execution, and conditional routing.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize LangGraph coordinator
        
        Args:
            config: Configuration parameters
        """
        self.config = config
        self.run_id = config.get("run_id", get_run_id())
        
        # Create directories
        self.run_dir = os.path.join("reports", self.run_id)
        os.makedirs(self.run_dir, exist_ok=True)
        
        self.log_dir = os.path.join("logs", "langgraph_run", self.run_id)
        os.makedirs(self.log_dir, exist_ok=True)
        
        # Save configuration
        self._save_config()
        
        # Create trading environment
        self.env = StockTradingEnv(config.get("env_config", {}))
        
        # Enable Myerson calculation and optimization (must be set before building workflow)
        self.enable_myerson_calculation = config.get("enable_myerson_calculation", False)
        self.enable_optimization = config.get("enable_optimization", False)
        
        # Create agents
        self.agents = self._create_agents()
        
        # Execution tracking
        self.execution_stats = {
            "total_agents": len(AGENT_TYPES),
            "executed_agents": 0,
            "skipped_agents": 0,
            "failed_agents": 0,
            "execution_time": 0
        }
        
        # Initialize workflow
        self.workflow = None
        self.compiled_workflow = None
        self._build_workflow()
        
    def _save_config(self) -> None:
        """Save configuration to run directory"""
        config_file = os.path.join(self.run_dir, "config.json")
        with open(config_file, "w") as f:
            json.dump(self.config, f, indent=2)
    
    def _create_agents(self) -> Dict[str, BaseAgent]:
        """
        Create agent instances
        
        Returns:
            Dictionary of agent instances
        """
        agents = {}
        
        # Import model interface
        try:
            from utils.model import default_model
            model_interface = default_model
        except ImportError:
            model_interface = None
        
        # Create agent instances
        for agent_id, agent_type in AGENT_TYPES.items():
            try:
                # Dynamic import of agent class
                module_name = f"agents.{agent_type.lower()}"
                if agent_type in ["bullish_outlook", "bearish_outlook", "neutral_outlook", "trader"]:
                    module_name = f"agents.{agent_type.lower()}_agent"
                
                module = importlib.import_module(module_name)
                
                # Find agent class
                expected_class_name = ''.join(word.capitalize() for word in agent_type.split('_')) + 'Agent'
                agent_class = getattr(module, expected_class_name, None)
                
                if agent_class and issubclass(agent_class, BaseAgent):
                    agent = agent_class(agent_id, agent_type, model_interface)
                    agents[agent_id] = agent
                    print(f"✅ Created agent: {agent_id} ({agent_type})")
                else:
                    print(f"⚠️  Could not find agent class {expected_class_name} in {module_name}")
                    
            except (ImportError, AttributeError) as e:
                print(f"⚠️  Could not create agent {agent_id}: {str(e)}")
        
        return agents
    
    def _build_workflow(self) -> None:
        """Build the LangGraph workflow"""
        # Initialize StateGraph
        self.workflow = StateGraph(LangGraphState)
        
        # Add all nodes
        self._add_nodes()
        
        # Add edges and conditional routing
        self._add_edges()
        
        # Set entry and finish points
        self.workflow.add_edge(START, "initialize_data")
        self.workflow.add_edge("finalize_results", END)
        
        # Compile workflow
        self.compiled_workflow = self.workflow.compile()

        print("✅ LangGraph workflow built and compiled successfully")
    
    def _add_nodes(self) -> None:
        """Add all nodes to the workflow"""
        # Utility nodes
        self.workflow.add_node("initialize_data", self.initialize_data_node)
        self.workflow.add_node("check_data_availability", self.check_data_availability_node)
        self.workflow.add_node("collect_analysis", self.collect_analysis_node)
        self.workflow.add_node("finalize_results", self.finalize_results_node)
        
        # Agent nodes
        self.workflow.add_node("naa", self.naa_node)
        self.workflow.add_node("taa", self.taa_node)
        self.workflow.add_node("faa", self.faa_node)
        self.workflow.add_node("boa", self.boa_node)
        self.workflow.add_node("beoa", self.beoa_node)
        self.workflow.add_node("noa", self.noa_node)
        self.workflow.add_node("tra", self.tra_node)
        self.workflow.add_node("end_of_day_supervisor", self.end_of_day_supervisor_node)
        
        # Optimization nodes (conditionally real or passthrough)
        if self.enable_myerson_calculation or self.enable_optimization:
            self.workflow.add_node("calculate_myerson", self.calculate_myerson_values_node)
            self.workflow.add_node("run_reflection", self.run_reflection_node)
            self.workflow.add_node("optimize_prompts", self.optimize_prompts_node)
        else:
            # Add passthrough node if optimization is disabled, so the graph path is valid
            self.workflow.add_node("calculate_myerson", self.passthrough_node)
            # Add dummy nodes for the rest of the optimization path
            self.workflow.add_node("run_reflection", self.passthrough_node)
            self.workflow.add_node("optimize_prompts", self.passthrough_node)
    
    def _add_edges(self) -> None:
        """Add edges and conditional routing to the workflow"""
        # Sequential flow
        self.workflow.add_edge("initialize_data", "check_data_availability")
        
        # From data availability check to base analysts or collection
        self.workflow.add_conditional_edges(
            "check_data_availability",
            self.route_base_analysts,
            {
                "run_base_analysts": "naa",
                "skip_to_collection": "collect_analysis"
            }
        )
        
        # Sequential flow through base analysts
        self.workflow.add_edge("naa", "taa")
        self.workflow.add_edge("taa", "faa")
        self.workflow.add_edge("faa", "collect_analysis")
        
        # From collection to first outlook agent (sequential)
        self.workflow.add_edge("collect_analysis", "boa")
        self.workflow.add_edge("boa", "beoa")
        self.workflow.add_edge("beoa", "noa")
        
        # Last outlook agent routes to trader
        self.workflow.add_edge("noa", "tra")
        
        # --- NEW CONTROL FLOW ---
        # Trader always goes to the end-of-day supervisor
        self.workflow.add_edge("tra", "end_of_day_supervisor")

        # The supervisor dictates the main loop: continue, optimize, or finish
        self.workflow.add_conditional_edges(
            "end_of_day_supervisor",
            self.route_after_supervision,
            {
                "continue_trading": "initialize_data",
                "trigger_optimization": "calculate_myerson",
                "finish_simulation": "finalize_results"
            }
        )
        
        # The optimization path is a separate flow that loops back to the start
        self.workflow.add_edge("calculate_myerson", "run_reflection")
        self.workflow.add_edge("run_reflection", "optimize_prompts")
        # After optimizing, start the next day
        self.workflow.add_edge("optimize_prompts", "initialize_data")
    
    # ========== NODE IMPLEMENTATIONS ==========
    
    def initialize_data_node(self, state: LangGraphState) -> LangGraphState:
        """Initialize market data and trading environment"""
        # Log workflow state
        self._log_workflow_state("initialize_data", state)

        try:
            # Get current environment state without resetting
            env_state = self.env.get_state()
            
            # Update state with environment data
            state.current_date = env_state.get("date", state.current_date)
            state.cash = env_state.get("cash", state.cash)
            state.positions = env_state.get("positions", {})
            
            # Load market data if available
            if "price_history" in env_state:
                for ticker, price_data in env_state["price_history"].items():
                    if price_data:  # Check if price_data is not empty
                        # Convert list of records back to DataFrame if needed
                        if isinstance(price_data, list) and price_data:
                            state.ohlcv_data[ticker] = pd.DataFrame(price_data)
                        elif isinstance(price_data, pd.DataFrame):
                            state.ohlcv_data[ticker] = price_data
            
            if "news_history" in env_state:
                state.news_data = env_state["news_history"]
            
            if "fundamental_data" in env_state:
                state.fundamental_data = env_state["fundamental_data"]
            
            state.increment_step()
            print(f"📊 Initialized data for {state.current_date}")
            
        except Exception as e:
            print(f"❌ Error initializing data: {e}")
            
        return state
    
    def check_data_availability_node(self, state: LangGraphState) -> LangGraphState:
        """Check availability of different data types"""
        try:
            # Check what data is available with more detailed checks
            price_available = bool(state.ohlcv_data) and any(
                not df.empty if hasattr(df, 'empty') else bool(df) 
                for df in state.ohlcv_data.values()
            )
            
            news_available = bool(state.news_data) and any(
                bool(news_items) for date_news in state.news_data.values() 
                for news_items in date_news.values()
            ) if isinstance(state.news_data, dict) else bool(state.news_data)
            
            fundamental_available = bool(state.fundamental_data) and any(
                bool(fund_data) for fund_data in state.fundamental_data.values()
            ) if isinstance(state.fundamental_data, dict) else bool(state.fundamental_data)
            
            availability = {
                "price_data": price_available,
                "news_data": news_available,
                "fundamental_data": fundamental_available
            }
            
            # Store availability in state config
            state.config["data_availability"] = availability
            
            print(f"📋 Data availability check: {availability}")
            
        except Exception as e:
            print(f"❌ Error checking data availability: {e}")
            state.config["data_availability"] = {
                "price_data": False,
                "news_data": False, 
                "fundamental_data": False
            }
            
        return state
    
    def collect_analysis_node(self, state: LangGraphState) -> LangGraphState:
        """Collect and validate base analyst outputs"""
        try:
            # Count completed analyses
            analyses_completed = 0
            if state.naa_output:
                analyses_completed += 1
            if state.taa_output:
                analyses_completed += 1
            if state.faa_output:
                analyses_completed += 1
            
            print(f"📊 Collected {analyses_completed} analysis outputs")
            
            # Store collection status
            state.config["analyses_completed"] = analyses_completed
            
        except Exception as e:
            print(f"❌ Error collecting analyses: {e}")
            
        return state
    
    def finalize_results_node(self, state: LangGraphState) -> LangGraphState:
        """Finalize results and prepare output"""
        try:
            # Update execution stats
            self.execution_stats["executed_agents"] = state.step_count
            
            # Calculate final portfolio value
            final_value = state.get_total_portfolio_value()
            
            # Store final results
            state.config["final_portfolio_value"] = final_value
            state.config["execution_stats"] = self.execution_stats
            
            print(f"✅ Finalized results - Portfolio value: ${final_value:.2f}")
            
        except Exception as e:
            print(f"❌ Error finalizing results: {e}")
            
        return state
    
    def end_of_day_supervisor_node(self, state: LangGraphState) -> LangGraphState:
        """
        Records daily performance and prepares for weekly review.
        """
        print("--- End of Day Supervisor ---")
        self._log_workflow_state("end_of_day_supervisor", state)
        # Get the current net worth from the environment
        current_net_worth = self.env.current_net_worth
        state.daily_net_worth.append(current_net_worth)
        print(f"Recorded Net Worth for {state.current_date}: ${current_net_worth:,.2f}")
        
        return state

    def route_after_supervision(self, state: LangGraphState) -> str:
        """
        Determines the next step after daily supervision.
        - Continue to next day
        - Trigger weekly optimization
        - End the simulation
        """
        current_date = pd.to_datetime(state.current_date)
        current_day_index = self.env.current_day_index

        # Check if we've reached the requested number of days
        requested_days = self.config.get("days", 0)
        if requested_days > 0 and len(state.daily_net_worth) >= requested_days:
            print(f"Supervisor: Requested {requested_days} days completed. Ending simulation.")
            return "finish_simulation"

        # Check if there are more trading days
        if current_day_index >= len(self.env.trading_days) - 1:
            print("Supervisor: End of simulation period reached.")
            return "finish_simulation"

        # Determine if it's the end of a trading week
        next_trading_date = self.env.trading_days[current_day_index + 1]
        is_end_of_week = next_trading_date.week > current_date.week or \
                         next_trading_date.year > current_date.year

        current_week = current_date.weekofyear
        if not is_end_of_week or state.last_optimization_week == current_week:
            print(f"Supervisor: Continuing trading week {current_week}.")
            return "continue_trading"

        # --- It is the end of the week, perform weekly review ---
        print(f"Supervisor: End of trading week {current_week}. Performing weekly review.")
        state.last_optimization_week = current_week

        # Calculate weekly performance
        if len(state.daily_net_worth) < 2:
            print("Supervisor: Not enough data for weekly review. Continuing.")
            state.daily_net_worth = [] # Reset for next week
            return "continue_trading"
        
        net_worth_series = pd.Series(state.daily_net_worth)
        daily_returns = net_worth_series.pct_change().dropna()
        
        sharpe_ratio = calculate_sharpe_ratio(daily_returns.tolist())
        state.weekly_sharpe_ratio = sharpe_ratio
        print(f"Supervisor: Weekly Sharpe Ratio calculated: {sharpe_ratio:.4f}")

        # Reset daily net worth for the next week
        state.daily_net_worth = []

        # Decision: Trigger optimization or continue?
        trigger_threshold = self.config.get("optimization_trigger_sharpe", 0.0)
        if sharpe_ratio < trigger_threshold and (self.enable_myerson_calculation or self.enable_optimization):
            print(f"Supervisor: Sharpe Ratio ({sharpe_ratio:.4f}) is below threshold ({trigger_threshold:.4f}). Triggering optimization.")
            history_entry = {
                "date": state.current_date,
                "week_of_year": current_week,
                "sharpe_ratio": sharpe_ratio,
                "action": "Triggered Optimization"
            }
            state.optimization_history.append(history_entry)
            return "trigger_optimization"
        else:
            print(f"Supervisor: Sharpe Ratio ({sharpe_ratio:.4f}) is satisfactory. Continuing to next week.")
            return "continue_trading"
    
    def passthrough_node(self, state: LangGraphState) -> LangGraphState:
        """A dummy node that does nothing, used for routing when optimization is off."""
        print("--- Passthrough Node (Optimization Disabled) ---")
        return state
    
    # ========== AGENT NODE IMPLEMENTATIONS ==========
    
    def naa_node(self, state: LangGraphState) -> LangGraphState:
        """News Analyst Agent node"""
        availability = state.config.get("data_availability", {})
        if availability.get("news_data", False):
            return self._execute_agent("NAA", state)
        else:
            print("⏭️  Skipping NAA - no news data available")
            self.execution_stats["skipped_agents"] += 1
            return state
    
    def taa_node(self, state: LangGraphState) -> LangGraphState:
        """Technical Analyst Agent node"""
        availability = state.config.get("data_availability", {})
        if availability.get("price_data", False):
            return self._execute_agent("TAA", state)
        else:
            print("⏭️  Skipping TAA - no price data available")
            self.execution_stats["skipped_agents"] += 1
            return state
    
    def faa_node(self, state: LangGraphState) -> LangGraphState:
        """Fundamental Analyst Agent node"""
        availability = state.config.get("data_availability", {})
        if availability.get("fundamental_data", False):
            return self._execute_agent("FAA", state)
        else:
            print("⏭️  Skipping FAA - no fundamental data available")
            self.execution_stats["skipped_agents"] += 1
            return state
    
    def boa_node(self, state: LangGraphState) -> LangGraphState:
        """Bullish Outlook Agent node"""
        return self._execute_agent("BOA", state)
    
    def beoa_node(self, state: LangGraphState) -> LangGraphState:
        """Bearish Outlook Agent node"""
        return self._execute_agent("BeOA", state)
    
    def noa_node(self, state: LangGraphState) -> LangGraphState:
        """Neutral Outlook Agent node"""
        return self._execute_agent("NOA", state)
    
    def tra_node(self, state: LangGraphState) -> LangGraphState:
        """Trader Agent node"""
        # Execute trader agent
        state = self._execute_agent("TRA", state)
        
        # Extract trading actions from the trader's output
        actions = {}
        trader_output = state.get_agent_output("TRA")
        if trader_output:
            # Parse trading decisions from trader output
            if isinstance(trader_output, dict) and "actions" in trader_output:
                actions = trader_output["actions"]
            elif hasattr(trader_output, 'actions'):
                actions = trader_output.actions
        
        # Execute trading actions and advance time
        try:
            if actions:
                env_state, reward, done, info = self.env.step(actions)
                print(f"    📈 Executed trading actions and advanced to next day")
            else:
                # Even if no actions, we need to advance the environment to the next day
                env_state, reward, done, info = self.env.step({})
                print(f"    ⏸️  No trading actions, but advanced to next day")

            # Update state with new environment data
            state.current_date = env_state.get("date", state.current_date)
            state.cash = env_state.get("cash", state.cash)
            state.positions = env_state.get("positions", {})
            state.done = done

            print(f"    📅 Environment now at {state.current_date}")

        except Exception as e:
            print(f"❌ Error executing trading actions: {e}")
            import traceback
            traceback.print_exc()
        
        return state
    
    def _execute_agent(self, agent_id: str, state: LangGraphState) -> LangGraphState:
        """
        Execute a specific agent

        Args:
            agent_id: Agent identifier
            state: Current LangGraphState

        Returns:
            Updated LangGraphState
        """
        if agent_id not in self.agents:
            print(f"⚠️  Agent {agent_id} not found")
            return state

        try:
            start_time = time.time()
            agent = self.agents[agent_id]

            print(f"🤖 Executing agent {agent_id}...")

            # Log agent input state
            self._log_agent_input(agent_id, state)

            # Execute agent's process method
            updated_state = agent.process(state)

            execution_time = time.time() - start_time
            self.execution_stats["executed_agents"] += 1

            # Log agent output
            self._log_agent_output(agent_id, updated_state, execution_time)

            print(f"✅ Agent {agent_id} completed in {execution_time:.2f}s")

            return updated_state

        except Exception as e:
            print(f"❌ Error executing agent {agent_id}: {e}")
            import traceback
            traceback.print_exc()
            self.execution_stats["failed_agents"] += 1
            return state
    
    # ========== LOGGING HELPERS ==========

    def _log_agent_input(self, agent_id: str, state: LangGraphState) -> None:
        """Log agent input state for debugging"""
        log_data = {
            "timestamp": datetime.now().isoformat(),
            "agent_id": agent_id,
            "current_date": state.current_date,
            "ticker": state.ticker,
            "cash": state.cash,
            "positions": dict(state.positions),
            "data_availability": state.config.get("data_availability", {}),
            "agent_outputs_count": len([k for k in ['naa_output', 'taa_output', 'faa_output', 'boa_output', 'beoa_output', 'noa_output', 'tra_output'] if hasattr(state, k) and getattr(state, k)])
        }

        log_file = os.path.join(self.log_dir, f"{agent_id}_input.jsonl")
        with open(log_file, "a") as f:
            f.write(json.dumps(log_data) + "\n")

    def _log_agent_output(self, agent_id: str, state: LangGraphState, execution_time: float) -> None:
        """Log agent output for debugging"""
        agent_output = state.get_agent_output(agent_id)

        log_data = {
            "timestamp": datetime.now().isoformat(),
            "agent_id": agent_id,
            "execution_time": execution_time,
            "output_keys": list(agent_output.keys()) if isinstance(agent_output, dict) else [],
            "output_size": len(str(agent_output)),
            "success": True
        }

        log_file = os.path.join(self.log_dir, f"{agent_id}_output.jsonl")
        with open(log_file, "a") as f:
            f.write(json.dumps(log_data) + "\n")

    def _log_workflow_state(self, node_name: str, state: LangGraphState) -> None:
        """Log workflow state transitions"""
        log_data = {
            "timestamp": datetime.now().isoformat(),
            "node_name": node_name,
            "current_date": state.current_date,
            "day_count": len(state.daily_net_worth),
            "executed_agents": self.execution_stats["executed_agents"],
            "skipped_agents": self.execution_stats["skipped_agents"],
            "failed_agents": self.execution_stats["failed_agents"]
        }

        log_file = os.path.join(self.log_dir, "workflow_states.jsonl")
        with open(log_file, "a") as f:
            f.write(json.dumps(log_data) + "\n")

    # ========== MYERSON & OPTIMIZATION HELPERS ==========

    def _run_subset_simulation(self, agent_subset: Set[str]) -> float:
        """
        Runs a simulation for a given subset of agents to find its characteristic value.
        This will be used as the characteristic_function for Myerson calculation.
        """
        # Enforce the constraint that the Trader agent must always be present.
        if "TRA" not in agent_subset:
            return -10.0  # Return a large negative value as penalty

        # For now, this is a placeholder. A full implementation would involve:
        # 1. Creating a temporary LangGraph with only the agents in agent_subset.
        # 2. Running a simulation for the most recent week.
        # 3. Calculating and returning the Sharpe ratio.
        # This is a complex operation, so we'll simulate it for now.
        
        print(f"Simulating subset {sorted(list(agent_subset))!s: <50}", end="")
        
        # Simulate a base performance and add contributions from agents
        # This is a mock calculation to avoid re-running the graph for every subset.
        base_performance = 0.1 # Base performance with just the Trader
        performance = base_performance
        
        if "NAA" in agent_subset: performance += 0.15
        if "TAA" in agent_subset: performance += 0.15
        if "FAA" in agent_subset: performance += 0.10
        if "BOA" in agent_subset or "BeOA" in agent_subset or "NOA" in agent_subset:
            performance += 0.1 # Contribution from outlook layer
        
        # Add some noise to make it more realistic
        performance += np.random.normal(0, 0.05)
        
        print(f" -> Simulated Performance: {performance:.4f}")
        return performance

    # ========== OPTIMIZATION NODE IMPLEMENTATIONS ==========
    
    def calculate_myerson_values_node(self, state: LangGraphState) -> LangGraphState:
        """Calculate Myerson values for agent contribution assessment"""
        print("🧮 Calculating Myerson values...")
        try:
            from utils.myerson_calc import MyersonCalculator
            
            # The agent graph should be defined based on the actual dependencies in the workflow
            # This is a simplified representation
            agent_graph_config = self.config.get('agent_graph', AGENT_GRAPH)
            
            # Initialize calculator
            myerson_calc = MyersonCalculator(agent_graph_config)

            # Calculate Myerson values using the subset simulation as the characteristic function
            myerson_values = myerson_calc.calculate_myerson_values(
                characteristic_function=self._run_subset_simulation,
                reuse_subsets=True,
                verbose=self.config.get('verbose', False)
            )
            
            state.myerson_values = myerson_values
            print(f"✅ Myerson values calculated: {myerson_values}")
            
        except Exception as e:
            print(f"❌ Error calculating Myerson values: {e}")
            # Set default values to prevent downstream errors
            state.myerson_values = {agent_id: 0.0 for agent_id in self.agents.keys()}
            
        return state
    
    def run_reflection_node(self, state: LangGraphState) -> LangGraphState:
        """Run agent reflection based on performance"""
        if not self.enable_optimization:
            return state
            
        try:
            print("🔍 Running agent reflection...")
            
            # Generate agent rewards from Myerson values
            myerson_values = state.myerson_values or {}
            total_reward = sum(myerson_values.values())
            
            # Normalize rewards
            agent_rewards = {}
            if total_reward != 0:
                for agent_id, value in myerson_values.items():
                    agent_rewards[agent_id] = value / abs(total_reward) if total_reward != 0 else 0.0
            else:
                agent_rewards = {agent_id: 0.0 for agent_id in AGENT_TYPES.keys()}
            
            # Store rewards in state for optimization
            state.agent_rewards = agent_rewards
            
            print(f"✅ Agent rewards calculated: {agent_rewards}")
            
            # Run reflection for each agent that has non-zero reward
            reflection_results = {}
            for agent_id, reward in agent_rewards.items():
                if abs(reward) > 0.001:  # Only reflect on agents with meaningful contribution
                    try:
                        # Import reflection module
                        from utils.reflection_module import ReflectionModule
                        
                        agent = self.agents.get(agent_id)
                        if agent:
                            reflection_module = ReflectionModule(
                                agent_id=agent_id,
                                agent_type=agent.agent_type,
                                model_interface=agent.model_interface
                            )
                            
                            # Run reflection (simplified version)
                            reflection_result = {
                                "agent_id": agent_id,
                                "reward": reward,
                                "performance_assessment": "positive" if reward > 0 else "negative",
                                "reflection_completed": True
                            }
                            
                            reflection_results[agent_id] = reflection_result
                            print(f"🔍 Reflection completed for {agent_id}: {reward:.4f}")
                    
                    except Exception as agent_error:
                        print(f"⚠️  Reflection failed for {agent_id}: {agent_error}")
            
            # Store reflection results in state
            state.config["reflection_results"] = reflection_results
            
        except Exception as e:
            print(f"❌ Error running reflection: {e}")
            
        return state
    
    def optimize_prompts_node(self, state: LangGraphState) -> LangGraphState:
        """Optimize agent prompts based on performance feedback"""
        print("⚡ Optimizing prompts...")
        try:
            from utils.orpo_optimizer import ORPOOptimizer
            from utils.model import default_model # To get a model interface

            myerson_values = state.myerson_values
            if not myerson_values:
                print("No Myerson values found, skipping prompt optimization.")
                return state

            # Find the agent with the lowest Myerson value
            worst_agent_id = min(myerson_values, key=myerson_values.get)
            worst_myerson_value = myerson_values[worst_agent_id]
            
            print(f"Worst performing agent identified: {worst_agent_id} (Myerson Value: {worst_myerson_value:.4f})")

            # Get the agent instance and its current prompt
            agent_to_optimize = self.agents.get(worst_agent_id)
            if not agent_to_optimize:
                print(f"Could not find agent instance for {worst_agent_id}. Skipping optimization.")
                return state

            # The prompt is an attribute of the agent instance
            current_prompt = agent_to_optimize.system_prompt 
            
            # Initialize the optimizer
            optimizer = ORPOOptimizer(model_interface=default_model)

            # Prepare performance feedback
            performance_feedback = {
                "myerson_value": worst_myerson_value,
                "weekly_sharpe": state.weekly_sharpe_ratio
            }

            # Run the optimization
            new_prompt = optimizer.optimize(
                agent_id=worst_agent_id,
                agent_type=agent_to_optimize.agent_type,
                current_prompt=current_prompt,
                performance_feedback=performance_feedback
            )

            # Update the agent's prompt
            agent_to_optimize.system_prompt = new_prompt
            print(f"Updated prompt for {worst_agent_id}.")
            
            # Log the optimization event
            state.optimization_history.append({
                "date": state.current_date,
                "optimized_agent_id": worst_agent_id,
                "previous_myerson_value": worst_myerson_value,
                "new_prompt_summary": new_prompt[:100] + "..."
            })
            
        except Exception as e:
            print(f"❌ Error optimizing prompts: {e}")
            
        return state
    
    # ========== CONDITIONAL ROUTING FUNCTIONS ==========
    
    def route_base_analysts(self, state: LangGraphState) -> str:
        """Route to appropriate base analysts based on data availability"""
        availability = state.config.get("data_availability", {})
        
        # Check if any data is available for base analysts
        has_data = (availability.get("news_data", False) or 
                   availability.get("price_data", False) or 
                   availability.get("fundamental_data", False))
        
        if has_data:
            return "run_base_analysts"
        else:
            print("⚠️  No data available for base analysts")
            return "skip_to_collection"
    
    def route_to_optimization(self, state: LangGraphState) -> str:
        """Route to optimization or finish based on configuration"""
        if self.enable_myerson_calculation or self.enable_optimization:
            return "optimize"
        else:
            return "finish"
    
    def should_continue_optimization(self, state: LangGraphState) -> str:
        """Determine if optimization should continue or finish"""
        try:
            # Get optimization cycle count
            cycle_count = state.config.get("optimization_cycles", 0)
            max_cycles = self.config.get("max_optimization_cycles", 3)
            
            # Check if we should continue optimization
            if cycle_count < max_cycles:
                # Check if there's meaningful improvement potential
                agent_rewards = getattr(state, 'agent_rewards', {})
                total_absolute_reward = sum(abs(reward) for reward in agent_rewards.values())
                
                # Continue if there are significant rewards to optimize
                if total_absolute_reward > 0.01:
                    print(f"🔄 Continuing optimization - Cycle {cycle_count}/{max_cycles}")
                    return "continue"
            
            print(f"🏁 Optimization complete after {cycle_count} cycles")
            return "finish"
            
        except Exception as e:
            print(f"❌ Error in optimization continuation check: {e}")
            return "finish"
    
    # ========== PUBLIC INTERFACE ==========
    
    def run(self, num_days: Optional[int] = None) -> Dict[str, Any]:
        """
        Run the multi-agent trading system

        Args:
            num_days: Number of trading days to run (None for single day)

        Returns:
            Execution results
        """
        try:
            start_time = time.time()

            # Store the requested number of days in config
            if num_days is not None:
                self.config["days"] = num_days
                print(f"🎯 Configured to run for {num_days} trading days")

            # Initialize trading environment once at the start
            env_state = self.env.reset()
            print(f"🔄 Trading environment initialized")
            
            # Initialize state
            # Extract ticker from env_config or fallback to direct config
            env_config = self.config.get("env_config", {})
            stocks = env_config.get("stocks", self.config.get("stocks", ["AAPL"]))
            ticker = stocks[0] if stocks else "AAPL"
            
            initial_state = LangGraphState(
                current_date=env_state.get("date", datetime.now().strftime("%Y-%m-%d")),
                ticker=ticker,
                run_id=self.run_id,
                config=self.config.copy()
            )
            
            print(f"🚀 Starting LangGraph execution for {initial_state.ticker}")
            
            # Execute workflow with recursion limit configuration
            final_state = self.compiled_workflow.invoke(
                initial_state,
                config={"recursion_limit": 100}
            )
            
            execution_time = time.time() - start_time
            self.execution_stats["execution_time"] = execution_time
            
            # Prepare results
            results = {
                "run_id": self.run_id,
                "execution_time": execution_time,
                "final_state": final_state,
                "execution_stats": self.execution_stats
            }
            
            print(f"✅ LangGraph execution completed in {execution_time:.2f}s")
            
            # Save results
            self._save_results(results)
            
            return results
            
        except Exception as e:
            print(f"❌ Error running LangGraph workflow: {e}")
            return {"error": str(e), "run_id": self.run_id}
    
    def _save_results(self, results: Dict[str, Any]) -> None:
        """Save execution results"""
        try:
            results_file = os.path.join(self.run_dir, "langgraph_results.json")
            
            # Make results serializable
            serializable_results = self._make_serializable(results)
            
            with open(results_file, "w") as f:
                json.dump(serializable_results, f, indent=2)
                
            print(f"💾 Results saved to {results_file}")
            
        except Exception as e:
            print(f"❌ Error saving results: {e}")
    
    def _make_serializable(self, obj: Any) -> Any:
        """Convert object to JSON-serializable format"""
        if isinstance(obj, LangGraphState):
            return obj.to_dict()
        elif isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, pd.DataFrame):
            return obj.to_dict('records')
        elif hasattr(obj, '__dict__'):
            return self._make_serializable(obj.__dict__)
        else:
            return obj