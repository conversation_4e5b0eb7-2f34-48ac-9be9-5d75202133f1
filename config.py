"""
多智能体美股交易系统的配置文件
"""
import os
from datetime import datetime

# 基本路径配置
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
DATA_DIR = os.path.join(BASE_DIR, "data")
LOGS_DIR = os.path.join(BASE_DIR, "logs")
REPORTS_DIR = os.path.join(BASE_DIR, "reports")

# 确保目录存在
for dir_path in [DATA_DIR, LOGS_DIR, REPORTS_DIR]:
    os.makedirs(dir_path, exist_ok=True)

# 智能体类型
AGENT_TYPES = {
    "NAA": "news_analyst",        # 新闻分析智能体
    "TAA": "technical_analyst",   # 技术面分析智能体
    "FAA": "fundamental_analyst", # 基本面分析智能体
    "BOA": "bullish_outlook",     # 看多展望智能体
    "BeOA": "bearish_outlook",    # 看空展望智能体
    "NOA": "neutral_outlook",     # 中立/观察智能体
    "TRA": "trader",              # 交易员智能体
}

# 智能体依赖关系图（用于Myerson值计算）
AGENT_GRAPH = {
    "NAA": ["BOA", "BeOA", "NOA"],  # 新闻分析直接连接到展望智能体
    "TAA": ["BOA", "BeOA", "NOA"],  # 技术分析直接连接到展望智能体
    "FAA": ["BOA", "BeOA", "NOA"],  # 基本面分析直接连接到展望智能体
    "BOA": ["TRA"],
    "BeOA": ["TRA"],
    "NOA": ["TRA"],
    "TRA": []  # 交易智能体是最终决策者，没有后续依赖
}

# 优化器类型配置
OPTIMIZER_TYPES = {
    "POMDP": "pomdp_optimizer",      # 基于POMDP的优化器
    "GPO": "gpo_optimizer",          # Generation-based Prompt Optimization
    "OPRO": "opro_optimizer",        # Optimization by PROmpting
    "MYERSON_OPRO": "myerson_opro_optimizer"  # 基于Myerson值奖励的OPRO优化器
}

# 默认优化器类型
DEFAULT_OPTIMIZER = "MYERSON_OPRO"

# Myerson-OPRO优化器配置
MYERSON_OPRO_CONFIG = {
    "max_optimization_steps": 15,      # 最大优化步数
    "history_prompts_k": 6,            # 历史提示数量
    "max_num_generated_instructions": 3,  # 每次生成的候选指令数量
    "plateau_patience": 4,             # 性能停滞容忍度
    "memory_size": 80,                 # 历史轨迹最大存储量
    "myerson_weight": 1.0,             # Myerson值奖励权重
    "smooth_reward": True,             # 是否使用平滑奖励
    "reward_alpha": 0.7,               # 奖励平滑的指数移动平均权重
}

# 交易环境配置
TRADING_ENV_CONFIG = {
    "starting_cash": 100_000,  # 初始资金10万美元
    "trading_fee_rate": 0.001,   # 交易费率0.1%
    "price_window": 26,          # 价格历史窗口
    "news_window": 7,            # 新闻历史窗口
    "fundamental_window": 4,     # 基本面数据历史窗口（季度）
}

# 模型配置
# 默认模型选择（可选："glm-4-flash"或"lm-studio-local"）
# DEFAULT_MODEL = "gemini-2.0-flash"
DEFAULT_MODEL = "glm-4-flash"  # 智谱AI API密钥需要更新
# DEFAULT_MODEL = "lm-studio-local"  # 临时使用本地模型
# 不同模型的配置
MODEL_CONFIGS = {
    "glm-4-flash": {
        # 注意：当前API密钥可能已过期，请到 https://open.bigmodel.cn/ 获取新的API密钥
        # 获取步骤：1. 注册/登录智谱AI开放平台 2. 创建API密钥 3. 替换下面的api_key值
        "api_key": "9ed29d1423fc4579878af74fda59d3a5.67MbiyDskKWvudWn",
        "base_url": "https://open.bigmodel.cn/api/paas/v4/chat/completions",
        "temperature": 0.0,
        "max_tokens": 1024,
        "top_p": 0.95,
    },
    "lm-studio-local": {
        "base_url": "http://192.168.31.94:1234",
        "api_type": "openai",
        "temperature": 0.0,
        "max_tokens": 1024,
        "top_p": 0.95,
    },
    "gemini-2.0-flash": {
        "api_key": "AIzaSyBIJQuF18odZzEy1UV9q9lE03feooytPAs",
        "api_type": "google",
        "base_url": "https://generativelanguage.googleapis.com/v1beta",
        "temperature": 0.0,
        "max_tokens": 1024,
        "top_p": 0.95,
    },
    # "gemini-2.5-flash-preview-04-17": {
    #     "api_key": "AIzaSyAlm7jxrpU8OyFuK2pfFXf9A6YDVxUmhvc",
    #     "temperature": 0.0,
    #     "max_tokens": 1024,
    #     "top_p": 0.95,
    # },
}

# 兼容旧版本代码的配置
# 注意：这是动态引用，会随DEFAULT_MODEL的变化而变化
def get_model_config():
    """获取当前默认模型的配置"""
    return MODEL_CONFIGS[DEFAULT_MODEL]

# 向后兼容，使MODEL_CONFIG始终引用当前默认模型的配置
MODEL_CONFIG = get_model_config()

# 运行配置
def get_run_id():
    """生成唯一的运行ID"""
    return datetime.now().strftime("multi_agent_run_%Y%m%d_%H%M%S")

# 默认股票列表（标普500指数成分股的一部分）
DEFAULT_STOCKS = [
    "AAPL", "MSFT", "AMZN", "GOOGL", "META", 
    "TSLA", "NVDA", "JPM", "V", "PG"
]

# ALPHAVANTAGE_API_KEY = "I6FSJGTBV4Z6RIYY"
# ALPHAVANTAGE_API_KEY = "Q2Z0UAE40MRV5Y1M"
# ALPHAVANTAGE_API_KEY = "7E38IQDCNN2HK7AA"
ALPHAVANTAGE_API_KEY = "D3UGF8SJ3WKUS4SC"
# Alpha Vantage free API limit is 25 requests per day. Please subscribe to any of the premium plans at https://www.alphavantage.co/premium/ to instantly remove all daily rate limits. 
POLYGON_API_KEY = "********************************.kWiEcnLQ5UiyYvGM"

# 本地嵌入服务器配置
EMBEDDING_SERVER_URL = "http://192.168.31.94:1234"
EMBEDDING_MODEL_NAME = "text-embedding-bge-base-zh-v1.5"