"""
Performance Metrics Calculation

A collection of functions to calculate various performance metrics for trading strategies.
"""
import numpy as np
from typing import List

def calculate_sharpe_ratio(daily_returns: List[float], risk_free_rate: float = 0.0) -> float:
    """
    Calculates the Sharpe Ratio from a list of daily returns.

    Args:
        daily_returns: A list of daily returns (e.g., [0.01, -0.005, ...]).
        risk_free_rate: The annual risk-free rate. Defaults to 0.0.

    Returns:
        The annualized Sharpe Ratio. Returns 0.0 if calculation is not possible.
    """
    if not daily_returns or len(daily_returns) < 2:
        return 0.0

    returns_array = np.array(daily_returns)
    
    # Calculate excess returns
    daily_risk_free_rate = (1 + risk_free_rate)**(1/252) - 1
    excess_returns = returns_array - daily_risk_free_rate
    
    # Calculate mean and standard deviation of excess returns
    mean_excess_return = np.mean(excess_returns)
    std_dev_excess_return = np.std(excess_returns)
    
    if std_dev_excess_return == 0:
        # If there's no volatility, Sharpe can be considered infinite if returns are positive,
        # but for practical purposes, we can return a large number or 0. Let's return 0 to avoid issues.
        return 0.0
        
    # Calculate annualized Sharpe Ratio
    sharpe_ratio = mean_excess_return / std_dev_excess_return
    annualized_sharpe_ratio = sharpe_ratio * np.sqrt(252) # Assuming 252 trading days in a year
    
    return annualized_sharpe_ratio 