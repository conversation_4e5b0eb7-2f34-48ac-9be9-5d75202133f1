"""
State Converter Utilities

Provides functions to convert between legacy dictionary format and LangGraphState
for backward compatibility during the transition to LangGraph.
"""

from typing import Dict, Any
import pandas as pd
from utils.langgraph_state import LangGraphState


def dict_to_langgraph_state(state_dict: Dict[str, Any]) -> LangGraphState:
    """
    Convert dictionary state to LangGraphState.
    
    This function handles the conversion from the legacy state format
    used by the original multi-agent system to the new LangGraphState format.
    
    Args:
        state_dict: Dictionary containing state data
        
    Returns:
        LangGraphState instance
    """
    # Extract required fields with defaults
    current_date = state_dict.get("current_date") or state_dict.get("date", "")
    ticker = state_dict.get("ticker", "AAPL")
    
    # Handle OHLCV data conversion
    ohlcv_data = {}
    if "ohlcv_data" in state_dict:
        ohlcv_data = state_dict["ohlcv_data"]
    elif "price_data" in state_dict and ticker:
        # Legacy price_data format
        ohlcv_data[ticker] = state_dict["price_data"]
    elif "price_history" in state_dict:
        # Another legacy format
        for stock, data in state_dict["price_history"].items():
            if isinstance(data, pd.DataFrame):
                ohlcv_data[stock] = data
            else:
                # Convert list of dicts to DataFrame if needed
                ohlcv_data[stock] = pd.DataFrame(data)
    
    # Create LangGraphState instance
    lang_state = LangGraphState(
        current_date=current_date,
        ticker=ticker,
        ohlcv_data=ohlcv_data,
        news_data=state_dict.get("news_data", []),
        fundamental_data=state_dict.get("fundamental_data", {}),
        cash=state_dict.get("cash", 100000.0),
        positions=state_dict.get("positions", {}),
        trading_history=state_dict.get("trading_history", []),
        config=state_dict.get("config", {}),
        run_id=state_dict.get("run_id", ""),
        step_count=state_dict.get("step_count", 0)
    )
    
    # Copy agent outputs if they exist
    if "naa_output" in state_dict:
        lang_state.naa_output = state_dict["naa_output"]
    if "taa_output" in state_dict:
        lang_state.taa_output = state_dict["taa_output"]
    if "faa_output" in state_dict:
        lang_state.faa_output = state_dict["faa_output"]
    if "boa_output" in state_dict:
        lang_state.boa_output = state_dict["boa_output"]
    if "beoa_output" in state_dict:
        lang_state.beoa_output = state_dict["beoa_output"]
    if "noa_output" in state_dict:
        lang_state.noa_output = state_dict["noa_output"]
    if "tra_output" in state_dict:
        lang_state.tra_output = state_dict["tra_output"]
    
    # Copy optimization data if it exists
    if "myerson_values" in state_dict:
        lang_state.myerson_values = state_dict["myerson_values"]
    if "agent_rewards" in state_dict:
        lang_state.agent_rewards = state_dict["agent_rewards"]
    
    # Copy model interfaces if they exist
    if "model_interfaces" in state_dict:
        lang_state.model_interfaces = state_dict["model_interfaces"]
    
    return lang_state


def langgraph_state_to_dict(state: LangGraphState) -> Dict[str, Any]:
    """
    Convert LangGraphState to dictionary for backward compatibility.
    
    This function creates a dictionary representation of the LangGraphState
    that is compatible with the legacy agent implementations.
    
    Args:
        state: LangGraphState instance
        
    Returns:
        Dictionary containing state data in legacy format
    """
    state_dict = {
        # Core data
        "current_date": state.current_date,
        "date": state.current_date,  # Alias for compatibility
        "ticker": state.ticker,
        "ohlcv_data": state.ohlcv_data,
        "news_data": state.news_data,
        "fundamental_data": state.fundamental_data,
        
        # Trading state
        "cash": state.cash,
        "positions": state.positions,
        "trading_history": state.trading_history,
        
        # Agent outputs
        "naa_output": state.naa_output,
        "taa_output": state.taa_output,
        "faa_output": state.faa_output,
        "boa_output": state.boa_output,
        "beoa_output": state.beoa_output,
        "noa_output": state.noa_output,
        "tra_output": state.tra_output,
        
        # Optimization data
        "myerson_values": state.myerson_values,
        "agent_rewards": state.agent_rewards,
        
        # Configuration and metadata
        "config": state.config,
        "model_interfaces": state.model_interfaces,
        "run_id": state.run_id,
        "step_count": state.step_count
    }
    
    # Add legacy aliases for compatibility
    if state.ticker in state.ohlcv_data:
        state_dict["price_data"] = state.ohlcv_data[state.ticker]
        state_dict["price_history"] = {state.ticker: state.ohlcv_data[state.ticker]}
    
    # Add analysis aliases for older agent implementations
    state_dict["news_analysis"] = state.naa_output
    state_dict["technical_analysis"] = state.taa_output
    state_dict["fundamental_analysis"] = state.faa_output
    state_dict["bullish_outlook"] = state.boa_output
    state_dict["bearish_outlook"] = state.beoa_output
    state_dict["neutral_outlook"] = state.noa_output
    
    return state_dict


def create_empty_langgraph_state(ticker: str = "AAPL", current_date: str = "", 
                                cash: float = 100000.0) -> LangGraphState:
    """
    Create an empty LangGraphState with basic initialization.
    
    Args:
        ticker: Stock ticker symbol
        current_date: Current date string
        cash: Initial cash amount
        
    Returns:
        Initialized LangGraphState instance
    """
    return LangGraphState(
        current_date=current_date,
        ticker=ticker,
        cash=cash
    )


def validate_state_compatibility(state_dict: Dict[str, Any]) -> bool:
    """
    Validate that a dictionary can be converted to LangGraphState.
    
    Args:
        state_dict: Dictionary to validate
        
    Returns:
        True if compatible, False otherwise
    """
    try:
        # Try to create LangGraphState from dict
        lang_state = dict_to_langgraph_state(state_dict)
        
        # Try to convert back to dict
        converted_dict = langgraph_state_to_dict(lang_state)
        
        return True
    except Exception:
        return False


def merge_agent_outputs(base_state: LangGraphState, agent_outputs: Dict[str, Dict[str, Any]]) -> LangGraphState:
    """
    Merge multiple agent outputs into a LangGraphState.
    
    Args:
        base_state: Base LangGraphState to update
        agent_outputs: Dictionary mapping agent IDs to their outputs
        
    Returns:
        Updated LangGraphState
    """
    for agent_id, output in agent_outputs.items():
        if output:  # Only update if output is not empty
            base_state.update_agent_output(agent_id, output)
    
    return base_state 