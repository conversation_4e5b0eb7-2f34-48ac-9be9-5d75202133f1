"""
LangGraph State Management

Defines the LangGraphState class for storing all agent collaboration information
in a structured, typed, and serializable format for LangGraph integration.
"""

import pandas as pd
from dataclasses import dataclass, field, asdict
from typing import Dict, List, Any, Optional
import json
import pickle
from datetime import datetime


@dataclass
class LangGraphState:
    """
    Comprehensive state class for LangGraph-based multi-agent collaboration.
    
    This class stores all information that agents need to share during collaboration,
    including market data, agent outputs, trading state, and optimization data.
    """
    
    # Core market data
    current_date: str
    ticker: str
    ohlcv_data: Dict[str, pd.DataFrame] = field(default_factory=dict)
    news_data: Dict[str, Dict[str, List[Dict[str, Any]]]] = field(default_factory=dict)
    fundamental_data: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    
    # Trading state
    cash: float = 100000.0
    positions: Dict[str, float] = field(default_factory=dict)
    trading_history: List[Dict[str, Any]] = field(default_factory=list)
    
    # Agent outputs
    naa_output: Dict[str, Any] = field(default_factory=dict)  # News Analyst Agent
    taa_output: Dict[str, Any] = field(default_factory=dict)  # Technical Analyst Agent
    faa_output: Dict[str, Any] = field(default_factory=dict)  # Fundamental Analyst Agent
    boa_output: Dict[str, Any] = field(default_factory=dict)  # Bullish Outlook Agent
    beoa_output: Dict[str, Any] = field(default_factory=dict) # Bearish Outlook Agent
    noa_output: Dict[str, Any] = field(default_factory=dict)  # Neutral Outlook Agent
    tra_output: Dict[str, Any] = field(default_factory=dict)  # Trader Agent
    
    # Optimization data
    myerson_values: Dict[str, float] = field(default_factory=dict)
    agent_rewards: Dict[str, float] = field(default_factory=dict)
    daily_net_worth: List[float] = field(default_factory=list)
    weekly_sharpe_ratio: float = 0.0
    optimization_history: List[Dict[str, Any]] = field(default_factory=list)
    last_optimization_week: int = -1
    
    # Configuration
    config: Dict[str, Any] = field(default_factory=dict)
    model_interfaces: Dict[str, Any] = field(default_factory=dict)
    
    # Metadata
    run_id: str = ""
    step_count: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert LangGraphState to dictionary for serialization.
        
        Note: pandas DataFrames are converted to JSON-serializable format.
        
        Returns:
            Dictionary representation of the state
        """
        state_dict = asdict(self)
        
        # Convert pandas DataFrames to serializable format
        if self.ohlcv_data:
            ohlcv_serializable = {}
            for ticker, df in self.ohlcv_data.items():
                if isinstance(df, pd.DataFrame):
                    # Convert DataFrame to dict with index as string
                    ohlcv_serializable[ticker] = {
                        'data': df.to_dict('records'),
                        'index': df.index.strftime('%Y-%m-%d').tolist() if hasattr(df.index, 'strftime') else df.index.tolist(),
                        'columns': df.columns.tolist()
                    }
                else:
                    ohlcv_serializable[ticker] = df
            state_dict['ohlcv_data'] = ohlcv_serializable
        
        return state_dict
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LangGraphState':
        """
        Create LangGraphState from dictionary.
        
        Args:
            data: Dictionary containing state data
            
        Returns:
            LangGraphState instance
        """
        # Handle pandas DataFrame reconstruction
        if 'ohlcv_data' in data and data['ohlcv_data']:
            ohlcv_data = {}
            for ticker, df_data in data['ohlcv_data'].items():
                if isinstance(df_data, dict) and 'data' in df_data:
                    # Reconstruct DataFrame from serialized format
                    df = pd.DataFrame(df_data['data'])
                    if 'index' in df_data:
                        df.index = pd.to_datetime(df_data['index'])
                    ohlcv_data[ticker] = df
                else:
                    ohlcv_data[ticker] = df_data
            data['ohlcv_data'] = ohlcv_data
        
        return cls(**data)
    
    def update_agent_output(self, agent_id: str, output: Dict[str, Any]) -> None:
        """
        Update specific agent output in the state.
        
        Args:
            agent_id: Agent identifier (NAA, TAA, FAA, BOA, BeOA, NOA, TRA)
            output: Agent's output data
        """
        agent_id_upper = agent_id.upper()
        
        if agent_id_upper == "NAA":
            self.naa_output = output
        elif agent_id_upper == "TAA":
            self.taa_output = output
        elif agent_id_upper == "FAA":
            self.faa_output = output
        elif agent_id_upper == "BOA":
            self.boa_output = output
        elif agent_id_upper == "BEOA":
            self.beoa_output = output
        elif agent_id_upper == "NOA":
            self.noa_output = output
        elif agent_id_upper == "TRA":
            self.tra_output = output
        else:
            raise ValueError(f"Unknown agent ID: {agent_id}")
    
    def get_agent_output(self, agent_id: str) -> Dict[str, Any]:
        """
        Get specific agent output from the state.
        
        Args:
            agent_id: Agent identifier
            
        Returns:
            Agent's output data
        """
        agent_id_upper = agent_id.upper()
        
        if agent_id_upper == "NAA":
            return self.naa_output
        elif agent_id_upper == "TAA":
            return self.taa_output
        elif agent_id_upper == "FAA":
            return self.faa_output
        elif agent_id_upper == "BOA":
            return self.boa_output
        elif agent_id_upper == "BEOA":
            return self.beoa_output
        elif agent_id_upper == "NOA":
            return self.noa_output
        elif agent_id_upper == "TRA":
            return self.tra_output
        else:
            return {}
    
    def get_agent_input_state(self, agent_id: str) -> Dict[str, Any]:
        """
        Get state formatted as dictionary for specific agent input.
        This maintains backward compatibility with existing agent implementations.
        
        Args:
            agent_id: Agent identifier
            
        Returns:
            Dictionary containing relevant state data for the agent
        """
        agent_id_upper = agent_id.upper()
        
        # Base state that all agents might need
        base_state = {
            "current_date": self.current_date,
            "date": self.current_date,  # Alias for compatibility
            "ticker": self.ticker,
            "cash": self.cash,
            "positions": self.positions,
            "trading_history": self.trading_history,
            "config": self.config,
            "run_id": self.run_id,
            "step_count": self.step_count
        }
        
        # Add agent-specific data
        if agent_id_upper == "NAA":
            base_state.update({
                "news_data": self.news_data
            })
        elif agent_id_upper == "TAA":
            base_state.update({
                "ohlcv_data": self.ohlcv_data,
                "price_data": self.ohlcv_data.get(self.ticker, pd.DataFrame())  # Alias
            })
        elif agent_id_upper == "FAA":
            base_state.update({
                "fundamental_data": self.fundamental_data
            })
        elif agent_id_upper in ["BOA", "BEOA", "NOA"]:
            # Outlook agents need analysis results
            base_state.update({
                "naa_output": self.naa_output,
                "taa_output": self.taa_output,
                "faa_output": self.faa_output,
                "news_analysis": self.naa_output,     # Aliases for compatibility
                "technical_analysis": self.taa_output,
                "fundamental_analysis": self.faa_output
            })
        elif agent_id_upper == "TRA":
            # Trader agent needs outlook results
            base_state.update({
                "boa_output": self.boa_output,
                "beoa_output": self.beoa_output,
                "noa_output": self.noa_output,
                "bullish_outlook": self.boa_output,   # Aliases for compatibility
                "bearish_outlook": self.beoa_output,
                "neutral_outlook": self.noa_output
            })
        
        return base_state
    
    def increment_step(self) -> None:
        """Increment the step counter."""
        self.step_count += 1
    
    def add_trading_record(self, action: str, ticker: str, quantity: float, 
                          price: float, timestamp: Optional[str] = None) -> None:
        """
        Add a trading record to history.
        
        Args:
            action: Trading action (BUY/SELL)
            ticker: Stock ticker
            quantity: Number of shares
            price: Price per share
            timestamp: Trade timestamp (defaults to current_date)
        """
        record = {
            "timestamp": timestamp or self.current_date,
            "action": action,
            "ticker": ticker,
            "quantity": quantity,
            "price": price,
            "total_value": quantity * price
        }
        self.trading_history.append(record)
    
    def update_position(self, ticker: str, quantity: float) -> None:
        """
        Update position for a ticker.
        
        Args:
            ticker: Stock ticker
            quantity: New quantity (positive for long, negative for short)
        """
        if quantity == 0:
            self.positions.pop(ticker, None)
        else:
            self.positions[ticker] = quantity
    
    def get_total_portfolio_value(self) -> float:
        """
        Calculate total portfolio value (cash + positions).
        
        Returns:
            Total portfolio value
        """
        total_value = self.cash
        
        # Add value of current positions
        for ticker, quantity in self.positions.items():
            if ticker in self.ohlcv_data and not self.ohlcv_data[ticker].empty:
                # Get current price (latest close price)
                current_price = self.ohlcv_data[ticker]['Close'].iloc[-1]
                total_value += quantity * current_price
        
        return total_value
    
    def is_serializable(self) -> bool:
        """
        Check if the state can be serialized to JSON.
        
        Returns:
            True if serializable, False otherwise
        """
        try:
            json.dumps(self.to_dict())
            return True
        except (TypeError, ValueError):
            return False
    
    def __str__(self) -> str:
        """String representation of the state."""
        return (f"LangGraphState(date={self.current_date}, ticker={self.ticker}, "
                f"cash=${self.cash:.2f}, positions={len(self.positions)}, "
                f"step={self.step_count})")
    
    def __repr__(self) -> str:
        """Detailed representation of the state."""
        return self.__str__() 