"""
Myerson-OPRO混合优化器

将Myerson值作为奖励信号结合OPRO方法的提示优化器
通过计算前后两次Myerson值的差值作为奖励，使用OPRO方法进行优化

修复说明（2025-01-30）：
- 修复了重复调用update_myerson_value导致的重复更新问题
- 在update()方法中直接计算Myerson奖励，避免与multi_agent_coordinator.py中的调用冲突
- 改进了调试输出，提供更详细的状态信息
"""
import os
import json
import numpy as np
import time
from datetime import datetime
from typing import Dict, List, Any, Set, Tuple, Optional, Union
import random
from copy import deepcopy

# 尝试导入 json_repair，如果不存在则提供警告
try:
    from json_repair import repair_json
    JSON_REPAIR_AVAILABLE = True
    print("json_repair 库已加载，JSON 修复功能已启用")
except ImportError:
    JSON_REPAIR_AVAILABLE = False
    print("警告: json_repair 库未安装。请运行 'pip install json_repair' 以获得更好的 JSON 修复能力")
    
    # 提供一个简单的备用函数
    def repair_json(malformed_json: str, return_objects: bool = True):
        """简单的 JSON 修复备用函数"""
        try:
            return json.loads(malformed_json)
        except:
            if return_objects:
                return {}
            else:
                return "{}"

class MyersonOPROOptimizer:
    """
    基于Myerson值奖励的OPRO优化器
    
    使用Optimization by PROmpting方法结合Myerson值差值作为奖励信号
    将OPRO视为POMDP的一种实现，其中：
    - 状态：当前提示、历史Myerson值、历史轨迹
    - 动作：生成新的优化提示
    - 奖励：Myerson值的差值 (ΔMyerson = Myerson_t+1 - Myerson_t)
    - 观测：奖励、Myerson值、下游反馈
    """
    
    def __init__(self, agent_id: str, agent_type: str, 
                 model_interface=None,
                 max_optimization_steps: int = 20,
                 history_prompts_k: int = 8,
                 max_num_generated_instructions: int = 4,
                 plateau_patience: int = 5,
                 memory_size: int = 100,
                 myerson_weight: float = 1.0,
                 smooth_reward: bool = True):
        """
        初始化Myerson-OPRO优化器
        
        参数:
            agent_id: 智能体ID
            agent_type: 智能体类型
            model_interface: 用于优化的模型接口
            max_optimization_steps: 最大优化步数
            history_prompts_k: 历史提示数量(k)
            max_num_generated_instructions: 每次生成的候选指令数量
            plateau_patience: 性能停滞容忍度
            memory_size: 历史轨迹最大存储量
            myerson_weight: Myerson值奖励权重
            smooth_reward: 是否使用平滑奖励
        """
        print(f"初始化Myerson-OPRO优化器 - 智能体: {agent_id}, 类型: {agent_type}")
        print(f"Myerson-OPRO参数: 最大步数={max_optimization_steps}, k={history_prompts_k}")
        print(f"Myerson奖励权重={myerson_weight}, 平滑奖励={smooth_reward}")
        
        self.agent_id = agent_id
        self.agent_type = agent_type
        self.model_interface = model_interface
        self.max_optimization_steps = max_optimization_steps
        self.history_prompts_k = history_prompts_k
        self.max_num_generated_instructions = max_num_generated_instructions
        self.plateau_patience = plateau_patience
        self.memory_size = memory_size
        self.myerson_weight = myerson_weight
        self.smooth_reward = smooth_reward
        
        # Myerson值历史跟踪
        self.myerson_history = []  # 存储 (step, myerson_value, prompt) 元组
        self.current_myerson_value = 0.0
        self.previous_myerson_value = 0.0
        
        # 优化轨迹：存储(prompt, myerson_reward, traditional_score)对
        self.optimization_trajectory = []
        
        # 当前优化状态
        self.current_step = 0
        self.current_prompt = ""
        self.current_myerson_reward = 0.0
        self.current_traditional_score = 0.0
        
        # 最佳参数 (基于Myerson奖励)
        self.best_prompt = ""
        self.best_myerson_reward = float('-inf')
        self.best_myerson_value = 0.0
        self.plateau_counter = 0
        
        # 奖励平滑参数
        self.reward_alpha = 0.7  # 指数移动平均的权重
        self.smoothed_myerson_reward = 0.0
        
        # 创建日志目录
        self.log_dir = os.path.join("logs", "optimizer", agent_type)
        os.makedirs(self.log_dir, exist_ok=True)
        
        # 为每个优化运行创建唯一ID
        self.run_id = f"{agent_id}_myerson_opro_{int(time.time())}"
        
    def update_myerson_value(self, myerson_value: float, prompt: str) -> float:
        """
        更新Myerson值并计算奖励
        
        参数:
            myerson_value: 当前的Myerson值
            prompt: 使用的提示
            
        返回:
            Myerson值差值奖励
        """
        # 更新Myerson值历史
        self.previous_myerson_value = self.current_myerson_value
        self.current_myerson_value = myerson_value
        
        # 计算Myerson值差值奖励
        if len(self.myerson_history) > 0:
            myerson_reward = myerson_value - self.previous_myerson_value
        else:
            # 第一次更新，使用当前值作为基线
            myerson_reward = 0.0
            
        # 平滑奖励处理
        if self.smooth_reward and len(self.myerson_history) > 0:
            self.smoothed_myerson_reward = (self.reward_alpha * myerson_reward + 
                                          (1 - self.reward_alpha) * self.smoothed_myerson_reward)
            final_reward = self.smoothed_myerson_reward
        else:
            final_reward = myerson_reward
            self.smoothed_myerson_reward = myerson_reward
            
        # 应用权重
        weighted_reward = self.myerson_weight * final_reward
        
        # 添加到历史记录
        entry = {
            "step": self.current_step,
            "myerson_value": myerson_value,
            "myerson_reward": myerson_reward,
            "smoothed_reward": self.smoothed_myerson_reward,
            "weighted_reward": weighted_reward,
            "prompt": prompt,
            "timestamp": datetime.now().isoformat()
        }
        self.myerson_history.append(entry)
        
        # 控制历史记录大小
        if len(self.myerson_history) > self.memory_size:
            self.myerson_history.pop(0)
            
        print(f"Myerson-OPRO智能体 {self.agent_id} - Myerson值更新:")
        print(f"  当前值: {myerson_value:.6f}, 前值: {self.previous_myerson_value:.6f}")
        print(f"  原始奖励: {myerson_reward:.6f}, 平滑奖励: {self.smoothed_myerson_reward:.6f}")
        print(f"  加权奖励: {weighted_reward:.6f}")
        
        return weighted_reward
        
    def update(self, prompt: str, traditional_score: float, myerson_value: float,
               task_examples: List[str] = None) -> None:
        """
        更新优化器，添加新的(prompt, myerson_reward, traditional_score)到优化轨迹中

        参数:
            prompt: 提示
            traditional_score: 传统评分
            myerson_value: Myerson值
            task_examples: 任务示例（保留接口兼容性）
        """
        # **修复递归调用问题：直接计算Myerson奖励，避免调用update_myerson_value**

        # 更新Myerson值历史
        self.previous_myerson_value = self.current_myerson_value
        self.current_myerson_value = myerson_value

        # 计算Myerson值差值奖励
        if len(self.myerson_history) > 0:
            myerson_reward = myerson_value - self.previous_myerson_value
        else:
            # 第一次更新，使用当前值作为基线
            myerson_reward = 0.0

        # 平滑奖励处理
        if self.smooth_reward and len(self.myerson_history) > 0:
            self.smoothed_myerson_reward = (self.reward_alpha * myerson_reward +
                                          (1 - self.reward_alpha) * self.smoothed_myerson_reward)
            final_reward = self.smoothed_myerson_reward
        else:
            final_reward = myerson_reward
            self.smoothed_myerson_reward = myerson_reward

        # 应用权重
        weighted_reward = self.myerson_weight * final_reward

        # 添加到Myerson历史记录（避免重复）
        myerson_entry = {
            "step": self.current_step,
            "myerson_value": myerson_value,
            "myerson_reward": myerson_reward,
            "smoothed_reward": self.smoothed_myerson_reward,
            "weighted_reward": weighted_reward,
            "prompt": prompt,
            "timestamp": datetime.now().isoformat()
        }
        self.myerson_history.append(myerson_entry)

        # 控制历史记录大小
        if len(self.myerson_history) > self.memory_size:
            self.myerson_history.pop(0)

        # 添加到优化轨迹
        trajectory_entry = {
            "prompt": prompt,
            "traditional_score": traditional_score,
            "myerson_value": myerson_value,
            "myerson_reward": myerson_reward,
            "combined_score": traditional_score + myerson_reward,  # 组合评分
            "step": self.current_step,
            "timestamp": datetime.now().isoformat()
        }
        self.optimization_trajectory.append(trajectory_entry)

        # 控制轨迹大小
        if len(self.optimization_trajectory) > self.memory_size:
            self.optimization_trajectory.pop(0)

        # 更新当前状态
        self.current_prompt = prompt
        self.current_myerson_reward = myerson_reward
        self.current_traditional_score = traditional_score

        # 检查是否需要更新最佳提示（基于Myerson奖励）
        if self.best_prompt == "" or myerson_reward > self.best_myerson_reward:
            print(f"Myerson-OPRO智能体 {self.agent_id} - 更新最佳提示")
            print(f"  新Myerson奖励: {myerson_reward:.6f}, 旧奖励: {self.best_myerson_reward:.6f}")

            self.best_myerson_reward = myerson_reward
            self.best_myerson_value = myerson_value
            self.best_prompt = prompt
            self.plateau_counter = 0

            self._save_best_prompt()
        else:
            print(f"Myerson-OPRO智能体 {self.agent_id} - 当前Myerson奖励未超过最佳，停滞计数 +1")
            self.plateau_counter += 1

        # 增加步数
        self.current_step += 1

        print(f"Myerson-OPRO智能体 {self.agent_id} - 更新完成，步骤: {self.current_step}")
        print(f"  当前Myerson值: {myerson_value:.6f}, 前值: {self.previous_myerson_value:.6f}")
        print(f"  原始奖励: {myerson_reward:.6f}, 平滑奖励: {self.smoothed_myerson_reward:.6f}")
        print(f"  传统分数: {traditional_score}, 加权奖励: {weighted_reward:.6f}")

        # 记录更新（简化版本，避免递归调用）
        if task_examples:
            print(f"  任务示例数量: {len(task_examples)}")

        # 保存状态到文件（如果需要）
        self._save_state_if_needed()

    def _save_state_if_needed(self) -> None:
        """
        根据需要保存状态到文件
        """
        # 每10步保存一次状态
        if self.current_step % 10 == 0:
            try:
                state_file = os.path.join(self.log_dir, f"{self.agent_id}_state.json")
                state_data = {
                    "current_step": self.current_step,
                    "current_myerson_value": self.current_myerson_value,
                    "best_myerson_reward": self.best_myerson_reward,
                    "plateau_counter": self.plateau_counter,
                    "trajectory_length": len(self.optimization_trajectory),
                    "history_length": len(self.myerson_history)
                }
                with open(state_file, 'w', encoding='utf-8') as f:
                    json.dump(state_data, f, indent=2, ensure_ascii=False)
            except Exception as e:
                print(f"保存状态文件失败: {e}")
        
    def get_optimized_prompt(self, current_prompt: str, current_traditional_score: float,
                            current_myerson_value: float,
                            task_examples: List[str] = None, 
                            num_candidates: int = 1) -> Dict[str, Any]:
        """
        生成一个基于Myerson值奖励优化的提示
        
        参数:
            current_prompt: 当前提示
            current_traditional_score: 当前传统评分
            current_myerson_value: 当前Myerson值
            task_examples: 任务示例
            num_candidates: 生成的候选提示数量
            
        返回:
            优化结果，包含优化的提示和详情
        """
        print(f"Myerson-OPRO智能体 {self.agent_id} - 当前步骤: {self.current_step}")
        print(f"  停滞计数: {self.plateau_counter}, 最佳Myerson奖励: {self.best_myerson_reward}")
        print(f"  当前Myerson值: {current_myerson_value}")
        
        # 检查是否应该停止优化
        if self.plateau_counter >= self.plateau_patience:
            print(f"Myerson-OPRO智能体 {self.agent_id} - 停滞计数超过容忍度，返回最佳提示")
            return {
                "optimized_prompt": self.best_prompt,
                "best_prompt": self.best_prompt,
                "myerson_reward": self.best_myerson_reward,
                "myerson_value": self.best_myerson_value,
                "details": "优化已达到停滞状态，返回最佳提示",
                "status": "plateau"
            }
            
        if self.current_step >= self.max_optimization_steps:
            print(f"Myerson-OPRO智能体 {self.agent_id} - 达到最大步数，返回最佳提示")
            return {
                "optimized_prompt": self.best_prompt,
                "best_prompt": self.best_prompt,
                "myerson_reward": self.best_myerson_reward,
                "myerson_value": self.best_myerson_value,
                "details": "已达到最大优化步数，返回最佳提示",
                "status": "max_steps"
            }
        
        # 生成基于Myerson值历史的OPRO元提示
        meta_prompt = self._generate_myerson_aware_meta_prompt(current_prompt, 
                                                              current_traditional_score,
                                                              current_myerson_value)
        
        # 生成候选提示
        candidates = []
        try:
            for i in range(self.max_num_generated_instructions):
                system_message = f"""你是一个专业的{self.agent_type}智能体提示优化专家。
你的目标是优化提示以最大化Myerson值奖励，这反映了智能体对整体交易收益的贡献。
Myerson值越高，说明智能体的决策对团队表现越重要。"""
                
                if self.model_interface and hasattr(self.model_interface, 'get_completion'):
                    response = self.model_interface.get_completion(
                        prompt=meta_prompt,
                        system_message=system_message
                    )
                    
                    # 从响应中提取指令
                    extracted_instruction = self._extract_instruction_from_response(response)
                    if extracted_instruction:
                        candidates.append(extracted_instruction)
                else:
                    print(f"警告: 模型接口不可用，无法生成候选提示")
                    candidates.append(current_prompt)
                    
        except Exception as e:
            print(f"生成候选提示时出错: {e}")
            return {
                "optimized_prompt": current_prompt,
                "best_prompt": self.best_prompt,
                "myerson_reward": 0.0,
                "details": f"生成候选提示失败: {str(e)}",
                "status": "error"
            }
            
        if not candidates:
            return {
                "optimized_prompt": current_prompt,
                "best_prompt": self.best_prompt,
                "myerson_reward": 0.0,
                "details": "未能生成候选提示",
                "status": "no_candidates"
            }
            
        # 评估所有候选提示并选择最佳的
        best_candidate = current_prompt  # 默认使用当前提示
        best_score = 0.0
        best_validation = None
        candidate_evaluations = []
        
        for i, candidate in enumerate(candidates):
            validation = self._validate_prompt_quality(candidate)
            candidate_evaluations.append({
                "candidate": candidate[:100] + "..." if len(candidate) > 100 else candidate,
                "validation": validation
            })
            
            print(f"📋 候选 {i+1} 质量评分: {validation['score']:.3f}, 有效: {validation['is_valid']}")
            
            if validation["is_valid"] and validation["score"] > best_score:
                best_candidate = candidate
                best_score = validation["score"]
                best_validation = validation
                print(f"🎯 更新最佳候选: 候选 {i+1}")
        
        # 如果没有找到有效的候选，使用当前提示或默认提示
        if best_score == 0.0:
            print("⚠️ 所有候选都无效，检查当前提示")
            current_validation = self._validate_prompt_quality(current_prompt)
            if current_validation["is_valid"]:
                best_candidate = current_prompt
                best_validation = current_validation
                print("✅ 使用当前提示")
            else:
                print("❌ 当前提示也无效，使用默认提示")
                best_candidate = self._get_default_prompt()
                best_validation = self._validate_prompt_quality(best_candidate)
        
        details = f"生成了 {len(candidates)} 个候选提示，"
        if best_validation:
            details += f"选择最佳候选(质量评分: {best_validation['score']:.3f})"
            if best_validation["issues"]:
                details += f"，存在问题: {'; '.join(best_validation['issues'][:2])}"
        else:
            details += "选择第一个候选"
        
        return {
            "optimized_prompt": best_candidate,
            "best_prompt": self.best_prompt,
            "myerson_reward": self.current_myerson_reward,
            "myerson_value": current_myerson_value,
            "details": details,
            "status": "success",
            "candidates": candidates[:3],  # 返回前3个候选供参考
            "quality_validation": best_validation,
            "candidate_evaluations": candidate_evaluations[:3]  # 返回前3个候选的评估结果
        }
        
    def _generate_myerson_aware_meta_prompt(self, current_prompt: str, 
                                          current_traditional_score: float,
                                          current_myerson_value: float) -> str:
        """
        生成考虑Myerson值历史的OPRO元提示
        
        参数:
            current_prompt: 当前提示
            current_traditional_score: 当前传统评分
            current_myerson_value: 当前Myerson值
            
        返回:
            元提示字符串
        """
        # 获取最近的k个历史记录
        recent_history = self._get_top_k_myerson_history_pairs()
        
        # 构建历史展示部分
        history_text = ""
        if recent_history:
            history_text = "=== 优化历史记录 ===\n"
            history_text += "以下是按Myerson值奖励排序的历史提示，请从中学习有效的模式：\n\n"
            
            for i, entry in enumerate(recent_history):
                myerson_reward = entry.get("myerson_reward", 0.0)
                myerson_value = entry.get("myerson_value", 0.0)
                traditional_score = entry.get("traditional_score", 0.0)
                prompt = entry.get("prompt", "")
                
                # 评估历史条目的表现
                performance_label = "🎯 优秀" if myerson_reward > 0.001 else "⚠️ 一般" if myerson_reward > -0.001 else "❌ 较差"
                
                history_text += f"【历史 {i+1} - {performance_label}】\n"
                history_text += f"  • Myerson奖励: {myerson_reward:.6f}\n"
                history_text += f"  • Myerson值: {myerson_value:.6f}\n" 
                history_text += f"  • 传统评分: {traditional_score:.4f}\n"
                history_text += f"  • 提示内容: {prompt[:200]}{'...' if len(prompt) > 200 else ''}\n\n"
        
        # 构建当前状态信息
        current_info = f"""=== 当前状态 ===
• Myerson值: {current_myerson_value:.6f}
• 传统评分: {current_traditional_score:.4f}
• 当前提示长度: {len(current_prompt)} 字符
• 当前提示: {current_prompt[:300]}{'...' if len(current_prompt) > 300 else ''}

"""
        
        # 构建优化目标说明
        optimization_goal = f"""=== 优化任务 ===
您是一位专业的提示词工程师，正在为 {self.agent_type} 智能体优化提示词。

核心目标: 最大化 Myerson 值奖励
• Myerson值反映该智能体对整体交易决策的贡献度
• 更高的Myerson值 = 更重要的分析贡献 = 更好的投资收益
• Myerson奖励 = 新Myerson值 - 旧Myerson值

成功标准:
✓ 生成正的Myerson奖励（表示改进）
✓ 保持或提升传统评分
✓ 符合{self.agent_type}的专业领域要求
✓ 提供具体、可操作的指导

"""
        
        # 检查当前提示是否包含JSON格式要求
        needs_json_format = any(keyword in current_prompt.lower() for keyword in [
            "json", "格式", "format", "结构化", "structured", "请回复", "请以", "返回json"
        ])
        
        # 根据智能体类型确定是否需要JSON格式
        json_output_agent_types = ["technical_analyst", "news_analyst", "fundamental_analyst", 
                                  "bullish_outlook", "bearish_outlook", "neutral_outlook", "trader"]
        requires_json = self.agent_type in json_output_agent_types or needs_json_format
        
        # 构建JSON格式要求
        json_instruction = ""
        if requires_json:
            json_instruction = """
=== 重要格式要求 ===
⚠️ 此智能体必须输出有效的JSON格式数据！

在新提示的末尾，必须包含清晰的JSON格式说明，例如：

"请严格按照以下JSON格式回复，不要添加任何额外文本：
{{
  \"analysis\": \"您的分析内容\",
  \"recommendation\": \"您的建议\",
  \"confidence\": \"信心度(0-1)\",
  \"reasoning\": \"推理过程\"
}}

注意：只返回有效的JSON对象，确保所有字符串都用双引号包围，不要添加注释或解释。"

格式检查清单：
✓ 包含明确的JSON结构示例
✓ 强调"只返回JSON对象"
✓ 警告不要添加额外文本
✓ 使用双引号而非单引号
"""
        
        # 构建完整的元提示
        meta_prompt = f"""{optimization_goal}

{history_text}

{current_info}

=== 提示词优化指南 ===
请基于历史记录和当前状态，生成一个改进的{self.agent_type}智能体提示词。

新提示词应该：
1. 🎯 针对{self.agent_type}的专业领域进行深度优化
2. 📈 从成功的历史记录中学习有效模式
3. ❌ 避免导致负Myerson奖励的提示模式
4. 🔧 提供具体、可操作的分析指导
5. 📋 保留所有重要的格式要求{json_instruction}

=== 输出格式 ===
请将优化后的提示词放在 <INSTRUCTION> 标签内：

<INSTRUCTION>
[在此处输出完整的优化提示词]
[如果需要JSON格式，请确保在末尾包含清晰的格式说明]
[不要在此标签内添加任何解释或注释]
</INSTRUCTION>

开始优化："""

        return meta_prompt
        
    def _get_top_k_myerson_history_pairs(self) -> List[Dict[str, Any]]:
        """
        获取按Myerson奖励排序的前k个历史记录
        
        返回:
            排序后的历史记录列表
        """
        if not self.optimization_trajectory:
            return []
            
        # 按Myerson奖励排序
        sorted_history = sorted(self.optimization_trajectory, 
                              key=lambda x: x.get("myerson_reward", 0.0), 
                              reverse=True)
        
        return sorted_history[:self.history_prompts_k]
        
    def _extract_instruction_from_response(self, response: str) -> str:
        """
        从模型响应中提取指令
        
        参数:
            response: 模型的响应文本
            
        返回:
            提取的指令字符串
        """
        import re
        import json
        
        if not response or not response.strip():
            print("⚠️ 响应为空，返回默认提示")
            return self._get_default_prompt()
        
        # 清理响应文本
        instruction = response.strip()
        
        print(f"🔍 开始提取指令，原始响应长度: {len(instruction)}")
        
        # 首先尝试提取<INSTRUCTION>标签内的内容
        instruction_match = re.search(r'<INSTRUCTION>\s*(.*?)\s*</INSTRUCTION>', instruction, re.DOTALL)
        if instruction_match:
            instruction = instruction_match.group(1).strip()
            print(f"✅ 成功从 <INSTRUCTION> 标签提取内容，长度: {len(instruction)}")
        else:
            print("⚠️ 未找到 <INSTRUCTION> 标签，使用完整响应")
        
        # 检查是否是JSON格式响应
        if instruction.startswith('{') or instruction.startswith('['):
            print("🔍 检测到可能的JSON格式响应，尝试解析...")
            
            # 首先尝试直接解析
            try:
                json_data = json.loads(instruction)
                print("✅ JSON直接解析成功")
                instruction = self._extract_prompt_from_json(json_data, response)
            except json.JSONDecodeError as e:
                print(f"❌ JSON直接解析失败: {e}")
                print(f"   - 导致错误的JSON片段 (前500字符): {instruction[:500]}")
                
                # 使用 json_repair 尝试修复
                if JSON_REPAIR_AVAILABLE:
                    print("🔧 使用 json_repair 尝试修复...")
                    try:
                        repaired_data = repair_json(instruction, return_objects=True)
                        if repaired_data:
                            print("✅ JSON修复成功")
                            instruction = self._extract_prompt_from_json(repaired_data, response)
                        else:
                            raise ValueError("json_repair 返回空结果")
                    except Exception as repair_e:
                        print(f"❌ JSON修复失败: {repair_e}")
                        instruction = self._fallback_text_extraction(instruction, response)
                else:
                    print("⚠️ json_repair 不可用，使用备用文本提取")
                    instruction = self._fallback_text_extraction(instruction, response)
        else:
            print("📝 检测到文本格式响应，进行文本处理")
            instruction = self._fallback_text_extraction(instruction, response)
        
        # 最终质量检查
        if not instruction or len(instruction.strip()) < 20:
            print("⚠️ 最终结果质量不佳，使用默认提示")
            instruction = self._get_default_prompt()
        
        print(f"✅ 指令提取完成，最终长度: {len(instruction)}")
        return instruction
    
    def _extract_prompt_from_json(self, json_data: Union[Dict, List], original_response: str) -> str:
        """
        从JSON数据中提取提示内容
        
        参数:
            json_data: 解析后的JSON数据
            original_response: 原始响应（备用）
            
        返回:
            提取的提示字符串
        """
        if isinstance(json_data, dict):
            # 查找可能包含提示的键
            prompt_keys = ['prompt', 'instruction', 'content', 'text', 'message', 'optimized_prompt', 'improved_prompt']
            
            for key in prompt_keys:
                if key in json_data:
                    value = json_data[key]
                    if isinstance(value, str) and len(value.strip()) > 20:  # 假设有效提示至少20个字符
                        print(f"✅ 从JSON键 '{key}' 提取提示，长度: {len(value.strip())}")
                        return value.strip()
            
            # 如果没有找到标准键，查找最长的字符串值
            longest_value = ""
            longest_key = ""
            for key, value in json_data.items():
                if isinstance(value, str) and len(value.strip()) > len(longest_value):
                    longest_value = value.strip()
                    longest_key = key
            
            if len(longest_value) > 20:
                print(f"✅ 使用最长字符串值 (键: '{longest_key}')，长度: {len(longest_value)}")
                return longest_value
            
        elif isinstance(json_data, list) and json_data:
            # 如果是列表，取第一个字符串元素
            for item in json_data:
                if isinstance(item, str) and len(item.strip()) > 20:
                    print(f"✅ 从JSON列表提取第一个有效字符串，长度: {len(item.strip())}")
                    return item.strip()
        
        print("⚠️ 无法从JSON中提取有效提示，使用原始响应")
        return self._fallback_text_extraction(original_response, original_response)
    
    def _fallback_text_extraction(self, text: str, original_response: str) -> str:
        """
        备用文本提取方法
        
        参数:
            text: 要处理的文本
            original_response: 原始响应
            
        返回:
            清理后的文本
        """
        print("🔧 执行备用文本提取...")
        
        # 移除常见的包装语句
        prefixes_to_remove = [
            "这是优化后的提示：", "优化的提示：", "新的提示：", "改进的提示：",
            "以下是优化的提示：", "优化后的", "新的", "改进的", "Here is the optimized prompt:",
            "Optimized prompt:", "New prompt:", "Improved prompt:"
        ]
        
        for prefix in prefixes_to_remove:
            if text.lower().startswith(prefix.lower()):
                text = text[len(prefix):].strip()
                print(f"🧹 移除前缀: {prefix}")
        
        # 移除引号
        if (text.startswith('"') and text.endswith('"')) or (text.startswith("'") and text.endswith("'")):
            text = text[1:-1].strip()
            print("🧹 移除外层引号")
        
        # 移除JSON字符串转义
        text = text.replace('\\"', '"').replace("\\'", "'")
        
        # 检查并修复JSON模板问题
        if '{' in text or '}' in text:
            text = self._fix_incomplete_json_template(text)
        
        # 最终验证
        if len(text.strip()) < 20:
            print("⚠️ 提取结果过短，使用默认提示")
            return self._get_default_prompt()
        
        print(f"✅ 备用提取完成，最终长度: {len(text.strip())}")
        return text.strip()
    
    def _get_default_prompt(self) -> str:
        """
        获取默认的智能体提示
        
        返回:
            默认提示字符串
        """
        agent_descriptions = {
            "technical_analyst": "技术分析师",
            "news_analyst": "新闻分析师", 
            "fundamental_analyst": "基本面分析师",
            "bullish_outlook": "看多分析师",
            "bearish_outlook": "看空分析师", 
            "neutral_outlook": "中性分析师",
            "trader": "交易员"
        }
        
        agent_name = agent_descriptions.get(self.agent_type, f"{self.agent_type}智能体")
        
        default_prompt = f"""你是一位专业的{agent_name}，请基于提供的数据进行深入分析。

核心要求：
1. 进行专业、客观的分析
2. 提供具体的见解和建议
3. 考虑风险因素和市场环境
4. 给出明确的结论和信心度

请严格按照以下JSON格式回复，不要添加任何额外文本：
{{
  "analysis": "您的专业分析内容",
  "recommendation": "您的建议",
  "confidence": "信心度(0-1之间的数值)",
  "reasoning": "分析推理过程",
  "risk_factors": "主要风险因素"
}}

注意：只返回有效的JSON对象，确保所有字符串都用双引号包围。"""
        
        print(f"🎯 使用{agent_name}的默认提示")
        return default_prompt
    
    def _validate_prompt_quality(self, prompt: str) -> Dict[str, Any]:
        """
        验证提示词的质量
        
        参数:
            prompt: 要验证的提示词
            
        返回:
            验证结果字典
        """
        validation_result = {
            "is_valid": True,
            "score": 0.0,
            "issues": [],
            "suggestions": []
        }
        
        # 基本长度检查
        if len(prompt.strip()) < 50:
            validation_result["issues"].append("提示词过短")
            validation_result["is_valid"] = False
        elif len(prompt.strip()) > 5000:
            validation_result["issues"].append("提示词过长，可能影响LLM处理")
            validation_result["score"] -= 0.1
        else:
            validation_result["score"] += 0.2
        
        # JSON格式要求检查
        json_keywords = ["json", "格式", "format", "结构化", "structured"]
        has_json_requirement = any(keyword in prompt.lower() for keyword in json_keywords)
        
        if has_json_requirement:
            # 检查是否有具体的JSON示例
            if "{" in prompt and "}" in prompt:
                validation_result["score"] += 0.3
            else:
                validation_result["issues"].append("声明需要JSON格式但缺少具体示例")
                validation_result["suggestions"].append("添加具体的JSON结构示例")
                validation_result["score"] -= 0.2
        
        # 专业性检查
        professional_keywords = ["分析", "评估", "建议", "风险", "市场", "投资", "交易"]
        professional_count = sum(1 for keyword in professional_keywords if keyword in prompt)
        
        if professional_count >= 3:
            validation_result["score"] += 0.2
        elif professional_count < 1:
            validation_result["issues"].append("缺少专业术语，可能不够专业")
            validation_result["suggestions"].append("添加更多行业相关的专业术语")
        
        # 清晰度检查
        if "请" in prompt or "Please" in prompt.lower():
            validation_result["score"] += 0.1
        
        if "不要" in prompt or "don't" in prompt.lower() or "避免" in prompt:
            validation_result["score"] += 0.1
        
        # 具体性检查
        specific_keywords = ["具体", "详细", "明确", "quantitative", "specific", "detailed"]
        if any(keyword in prompt for keyword in specific_keywords):
            validation_result["score"] += 0.1
        
        # 最终评分归一化
        validation_result["score"] = max(0.0, min(1.0, validation_result["score"]))
        
        # 根据评分确定是否有效
        if validation_result["score"] < 0.5:
            validation_result["is_valid"] = False
            validation_result["issues"].append(f"整体质量评分过低: {validation_result['score']:.2f}")
        
        return validation_result
    
    def _fix_incomplete_json_template(self, text: str) -> str:
        """
        修复不完整的JSON模板
        
        参数:
            text: 包含不完整JSON的文本
            
        返回:
            修复后的文本
        """
        import re
        
        print(f"🔧 开始修复JSON模板，原始长度: {len(text)}")
        
        # 首先，识别并保护完整的JSON示例
        # 查找完整的JSON对象（平衡的花括号）
        complete_json_pattern = r'\{(?:[^{}]|(?:\{[^{}]*\}))*\}'
        complete_jsons = re.findall(complete_json_pattern, text)
        
        print(f"🔍 找到 {len(complete_jsons)} 个完整的JSON结构")
        
        # 如果有完整的JSON结构，我们需要保护它们并转义花括号
        protected_text = text
        json_placeholders = {}
        
        for i, json_obj in enumerate(complete_jsons):
            # 转义JSON中的花括号以避免被format()误解
            escaped_json = json_obj.replace('{', '{{').replace('}', '}}')
            placeholder = f"__COMPLETE_JSON_{i}__"
            json_placeholders[placeholder] = escaped_json
            protected_text = protected_text.replace(json_obj, placeholder, 1)
            print(f"🛡️ 保护完整JSON {i}: {json_obj[:50]}...")
        
        # 现在处理不完整的JSON结构
        # 模式1: 不闭合的JSON对象（如 { "field": "value 没有闭合）
        pattern1 = r'\{\s*"[^"]*"\s*:\s*"[^"]*(?:[^}]*)$'
        if re.search(pattern1, protected_text):
            print("🧹 修复模式1: 不完整的JSON对象")
            protected_text = re.sub(pattern1, '', protected_text)
        
        # 模式2: 单独的字段名如 "market_assessment"（但不在完整JSON中）
        pattern2 = r'(?:^|\n)\s*"[^"]*"\s*(?:$|\n)'
        matches = re.findall(pattern2, protected_text)
        if matches:
            print(f"🧹 修复模式2: 找到 {len(matches)} 个单独的字段名")
            protected_text = re.sub(pattern2, '', protected_text)
        
        # 模式3: 处理剩余的不配对花括号
        # 移除孤立的开括号（不属于完整JSON的）
        protected_text = re.sub(r'\{(?!__COMPLETE_JSON_)', '', protected_text)
        # 移除孤立的闭括号（不属于完整JSON的）
        protected_text = re.sub(r'(?<!__)\}', '', protected_text)
        
        # 恢复完整的JSON结构（已转义）
        for placeholder, escaped_json in json_placeholders.items():
            protected_text = protected_text.replace(placeholder, escaped_json)
            print(f"🔄 恢复转义后的JSON: {escaped_json[:50]}...")
        
        # 清理多余的空白
        protected_text = re.sub(r'\n\s*\n\s*\n', '\n\n', protected_text)  # 最多保留两个连续换行
        protected_text = re.sub(r'[ \t]+', ' ', protected_text)  # 合并多余空格，但保留换行
        protected_text = protected_text.strip()
        
        print(f"✅ JSON模板修复完成，最终长度: {len(protected_text)}")
        
        return protected_text
        
    def _save_best_prompt(self) -> None:
        """
        保存最佳提示到文件
        """
        try:
            best_data = {
                "agent_id": self.agent_id,
                "agent_type": self.agent_type,
                "best_prompt": self.best_prompt,
                "best_myerson_reward": self.best_myerson_reward,
                "best_myerson_value": self.best_myerson_value,
                "optimization_step": self.current_step,
                "timestamp": datetime.now().isoformat(),
                "run_id": self.run_id
            }
            
            filename = f"best_prompt_myerson_opro_{self.agent_id}.json"
            filepath = os.path.join(self.log_dir, filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(best_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存最佳提示时出错: {e}")
            
    def _log_update(self, prompt: str, traditional_score: float, 
                   myerson_value: float, myerson_reward: float,
                   task_examples: List[str] = None) -> None:
        """
        记录更新信息
        
        参数:
            prompt: 提示
            traditional_score: 传统评分
            myerson_value: Myerson值
            myerson_reward: Myerson值奖励
            task_examples: 任务示例
        """
        try:
            log_data = {
                "step": self.current_step,
                "agent_id": self.agent_id,
                "agent_type": self.agent_type,
                "prompt": prompt,
                "traditional_score": traditional_score,
                "myerson_value": myerson_value,
                "myerson_reward": myerson_reward,
                "smoothed_reward": self.smoothed_myerson_reward,
                "best_myerson_reward": self.best_myerson_reward,
                "plateau_counter": self.plateau_counter,
                "timestamp": datetime.now().isoformat(),
                "run_id": self.run_id
            }
            
            filename = f"myerson_opro_log_{self.agent_id}_{self.run_id}.jsonl"
            filepath = os.path.join(self.log_dir, filename)
            
            with open(filepath, 'a', encoding='utf-8') as f:
                f.write(json.dumps(log_data, ensure_ascii=False) + '\n')
                
        except Exception as e:
            print(f"记录更新信息时出错: {e}")
            
    def load_best_prompt(self) -> Dict[str, Any]:
        """
        加载最佳提示
        
        返回:
            最佳提示信息字典
        """
        try:
            filename = f"best_prompt_myerson_opro_{self.agent_id}.json"
            filepath = os.path.join(self.log_dir, filename)
            
            if os.path.exists(filepath):
                with open(filepath, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                return {}
                
        except Exception as e:
            print(f"加载最佳提示时出错: {e}")
            return {}
            
    def get_state_summary(self) -> Dict[str, Any]:
        """
        获取优化器状态摘要
        
        返回:
            状态摘要字典
        """
        return {
            "agent_id": self.agent_id,
            "agent_type": self.agent_type,
            "current_step": self.current_step,
            "current_myerson_value": self.current_myerson_value,
            "current_myerson_reward": self.current_myerson_reward,
            "smoothed_myerson_reward": self.smoothed_myerson_reward,
            "best_myerson_reward": self.best_myerson_reward,
            "best_myerson_value": self.best_myerson_value,
            "plateau_counter": self.plateau_counter,
            "trajectory_length": len(self.optimization_trajectory),
            "myerson_history_length": len(self.myerson_history),
            "run_id": self.run_id
        } 