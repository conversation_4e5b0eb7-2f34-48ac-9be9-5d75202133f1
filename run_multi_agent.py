#!/usr/bin/env python3
"""
多智能体美股交易系统主运行脚本

启动并运行多智能体美股交易系统
"""
import os
import sys
import json
import argparse
import matplotlib.pyplot as plt
import pandas as pd
from datetime import datetime
from typing import Dict, Any, List
import math # 导入 math 模块用于检查 NaN
from pandas import Timestamp # 导入 Timestamp
import pdb

# 修改导入路径，使用相对导入
from config import TRADING_ENV_CONFIG, DEFAULT_MODEL, MODEL_CONFIG, get_run_id
from langgraph_coordinator import LangGraphCoordinator
from data.get_all_data import manage_stock_data # <-- Import the data management function

# 自定义 JSON Encoder 以处理 Timestamp 和 NaN
class CustomJSONEncoder(json.JSONEncoder):
    def default(self, o):
        if isinstance(o, Timestamp):
            return o.isoformat() # 将 Timestamp 转换为 ISO 格式字符串
        if isinstance(o, float) and math.isnan(o):
            return None # 将 NaN 转换为 None
        if hasattr(o, 'to_dict'):
            return o.to_dict() # 将 DataFrame 转换为字典
        if hasattr(o, 'tolist'):
            return o.tolist() # 将 numpy 数组转换为列表
        return super().default(o)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="运行多智能体美股交易系统")
    
    # 基本配置
    parser.add_argument("--run_id", type=str, default="", help="运行ID，默认自动生成")
    parser.add_argument("--days", type=int, default=0, help="运行天数，默认运行到环境结束")
    parser.add_argument("--model", type=str, default=DEFAULT_MODEL, help=f"使用的模型，默认为{DEFAULT_MODEL}")
    
    # 环境配置
    parser.add_argument("--start_date", type=str, default="2025-01-01", help="起始日期，格式YYYY-MM-DD")
    parser.add_argument("--end_date", type=str, default="2025-03-31", help="结束日期，格式YYYY-MM-DD")
    parser.add_argument("--tickers", type=str, nargs='+', required=True, help="一个或多个股票代码，例如 AAPL MSFT GOOG")
    parser.add_argument("--starting_cash", type=float, default=TRADING_ENV_CONFIG["starting_cash"], help="初始资金")
    
    # 优化配置
    parser.add_argument("--optimize", action="store_true", default=True, help="启用提示优化")
    parser.add_argument("--optimizer", type=str, choices=["pomdp", "gpo", "opro", "myerson_opro"], default="myerson_opro", help="使用的优化器类型，默认为Myerson_OPRO")
    parser.add_argument("--exploration_rate", type=float, default=0.1, help="POMDP探索率")
    
    # GPO优化器配置
    parser.add_argument("--max_optimization_steps", type=int, default=20, help="优化器最大优化步数")
    parser.add_argument("--history_prompts_k", type=int, default=3, help="GPO历史提示数量(k)，OPRO默认使用8")
    parser.add_argument("--initial_max_edit_words", type=int, default=50, help="GPO初始最大编辑词数")
    parser.add_argument("--plateau_patience", type=int, default=3, help="性能停滞容忍度")
    
    # OPRO优化器配置
    parser.add_argument("--max_num_generated_instructions", type=int, default=4, help="OPRO每次生成的候选指令数量")
    
    # Myerson-POMDP 集成配置
    parser.add_argument("--use_myerson", action="store_true", help="使用 Myerson 值计算智能体贡献")
    parser.add_argument("--myerson_cache_dir", type=str, default="", help="Myerson LLM 调用缓存目录，默认为 'logs/llm_cache/{run_id}'")
    parser.add_argument("--use_llm_characteristic", action="store_true", help="使用 LLM 评估智能体子集的特征函数")
    parser.add_argument("--myerson_pomdp_integration", action="store_true", help="使用 Myerson-POMDP 集成框架优化提示词")
    parser.add_argument("--optimization_trigger_sharpe", type=float, default=0.0, help="触发优化的夏普比率阈值，默认为0.0")
    
    # 其他配置
    parser.add_argument("--verbose", action="store_true", help="详细输出")
    parser.add_argument("--load_best_params", action="store_true", help="加载最佳参数")
    parser.add_argument("--no_reflection", action="store_true", help="禁用反思机制")
    parser.add_argument("--plot", action="store_true", help="生成收益率曲线图")
    
    return parser.parse_args()

def validate_args(args):
    """验证参数"""
    from datetime import datetime
    
    try:
        start_date = datetime.strptime(args.start_date, "%Y-%m-%d")
        end_date = datetime.strptime(args.end_date, "%Y-%m-%d")
        
        if start_date >= end_date:
            raise ValueError("开始日期必须早于结束日期")
            
        if args.days < 0:
            raise ValueError("运行天数必须大于等于0")
            
        if args.starting_cash <= 0:
            raise ValueError("初始资金必须大于0")
            
    except ValueError as e:
        raise ValueError(f"参数验证失败: {e}")

def validate_trading_environment_setup(start_date: str, end_date: str, stocks: List[str]):
    """验证交易环境设置，确保数据可用性"""
    import sqlite3
    
    print(f"验证交易环境设置...")
    print(f"日期范围: {start_date} 到 {end_date}")
    print(f"股票: {stocks}")
    
    for stock in stocks:
        db_path = f"data/tickers/{stock}/{stock}_data.db"
        
        if not os.path.exists(db_path):
            raise ValueError(f"数据库不存在: {db_path}")
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        try:
            # 检查数据可用性
            cursor.execute("""
                SELECT COUNT(*) FROM ohlcv 
                WHERE ticker = ? AND trade_date BETWEEN ? AND ?
            """, (stock, start_date, end_date))
            
            count = cursor.fetchone()[0]
            
            if count == 0:
                # 查看实际可用范围
                cursor.execute("SELECT MIN(trade_date), MAX(trade_date) FROM ohlcv WHERE ticker = ?", (stock,))
                result = cursor.fetchone()
                if result and result[0]:
                    min_date, max_date = result
                    raise ValueError(f"""
                {stock} 在请求的日期范围内没有数据！
                请求范围: {start_date} 到 {end_date}
                实际范围: {min_date} 到 {max_date}
                请调整日期范围或重新下载数据。
                    """)
                else:
                    raise ValueError(f"{stock} 数据库中没有任何数据！")
            
            # 检查日期间隙
            cursor.execute("""
                SELECT trade_date FROM ohlcv 
                WHERE ticker = ? AND trade_date BETWEEN ? AND ?
                ORDER BY trade_date ASC
            """, (stock, start_date, end_date))
            
            dates = [row[0] for row in cursor.fetchall()]
            large_gaps = []
            
            for i in range(1, len(dates)):
                prev_date = pd.to_datetime(dates[i-1])
                curr_date = pd.to_datetime(dates[i])
                days_diff = (curr_date - prev_date).days
                
                if days_diff > 14:  # 超过两周的间隙
                    large_gaps.append((dates[i-1], dates[i], days_diff))
            
            if large_gaps:
                gap_details = "\n".join([f"  {prev} -> {curr} (间隔 {days} 天)" for prev, curr, days in large_gaps])
                print(f"⚠️ 警告: {stock} 存在 {len(large_gaps)} 个大的日期间隙:")
                print(gap_details)
                print("  这可能导致交易环境出现日期跳跃问题！")
            
            print(f"✅ {stock}: 找到 {count} 条记录，数据基本可用")
            
        finally:
            conn.close()
    
    print("✅ 交易环境设置验证完成")

def main():
    """主函数"""
    args = parse_args()
    
    # 验证参数
    validate_args(args)
    
    # 新增：验证交易环境设置
    try:
        validate_trading_environment_setup(args.start_date, args.end_date, args.tickers)
    except ValueError as e:
        print(f"❌ 交易环境设置验证失败: {e}")
        print("请检查日期范围和数据可用性后重试。")
        return 1
    
    base_run_id = args.run_id if args.run_id else get_run_id()
    base_run_dir = os.path.join("reports", base_run_id)
    os.makedirs(base_run_dir, exist_ok=True)
    
    # 准备基础配置 (不包含特定ticker)
    base_config = prepare_base_config(args, base_run_id)
    
    # 保存基础配置
    base_config_path = os.path.join(base_run_dir, "config.json")
    with open(base_config_path, "w") as f:
        json.dump(base_config, f, indent=2, cls=CustomJSONEncoder)
    print(f"基础配置已保存到: {base_config_path}")

    all_results_summary = []

    for ticker in args.tickers:
        print(f"\n{'='*20} 开始处理股票: {ticker} {'='*20}")
        ticker_specific_identifier = f"{base_run_id}_{ticker}" 
        ticker_run_dir = os.path.join(base_run_dir, ticker)
        os.makedirs(ticker_run_dir, exist_ok=True)

        ticker_config = prepare_ticker_specific_config(base_config, ticker, ticker_specific_identifier)
    
        print(f"开始为股票 {ticker} 准备数据 (范围: {args.start_date} - {args.end_date})...")
        data_ready = False
        try:
            data_ready = manage_stock_data(ticker, args.start_date, args.end_date, args.verbose)
        except Exception as e:
            print(f"为股票 {ticker} 调用 manage_stock_data 时发生严重错误: {e}. 跳过该股票。", file=sys.stderr)
            import traceback
            traceback.print_exc(file=sys.stderr)
            all_results_summary.append({
                "ticker": ticker, 
                "run_id": ticker_specific_identifier,
                "status": "data_preparation_critical_error",
                "error_message": str(e)
            })
            data_ready = False # Explicitly set to false
        
        if not data_ready:
            print(f"股票 {ticker} 的数据准备失败或未完成。跳过该股票的模拟交易。", file=sys.stderr)
            # Add to summary even if it was added in exception block, to ensure it's there with proper status
            # if not any(s['ticker'] == ticker and s['status'] == "data_preparation_critical_error" for s in all_results_summary):
            # Simplified: an error during data prep means we add this error summary
            # Check if it was already added by the exception block above to avoid duplicates for the same reason
            already_added_as_critical_error = any(
                s.get('ticker') == ticker and s.get('status') == "data_preparation_critical_error"
                for s in all_results_summary
            )
            if not already_added_as_critical_error:
                all_results_summary.append({
                    "ticker": ticker, 
                    "run_id": ticker_specific_identifier,
                    "status": "data_preparation_failed", # General failure from manage_stock_data returning False
                    "error_message": "manage_stock_data returned False, see logs from get_all_data.py for details."
                })
            print(f"{'='*20} 股票: {ticker} 数据准备失败，处理中止 {'='*20}")
            continue # Skip to the next ticker
            
        print(f"股票 {ticker} 的数据准备完成。")

        coordinator = LangGraphCoordinator(ticker_config)
        print(f"开始为股票 {ticker} 运行多智能体系统...")
        
        result = None 
        try:
            langgraph_result = coordinator.run(args.days if args.days > 0 else None)
            
            # Extract and adapt LangGraph result to maintain compatibility
            result = adapt_langgraph_result(langgraph_result, ticker, ticker_specific_identifier)

            result_path = os.path.join(ticker_run_dir, "result.json")
            with open(result_path, "w") as f:
                json.dump(result, f, indent=2, cls=CustomJSONEncoder)
            print(f"股票 {ticker} 的详细运行结果已保存到: {result_path}")
    
            print_summary(result, ticker, result_path)
            all_results_summary.append(result)
    
            if args.plot and 'daily_returns' in result:
                plot_returns(result, ticker_run_dir, ticker)
        except Exception as e:
            print(f"为股票 {ticker} 运行模拟时出错: {e}", file=sys.stderr)
            import traceback
            traceback.print_exc(file=sys.stderr)
            error_result = {"ticker": ticker, "run_id": ticker_specific_identifier, "status": "run_error", "error_message": str(e)}
            if result and isinstance(result, dict):
                result.update(error_result)
            else:
                result = error_result
            all_results_summary.append(result)
        
        print(f"{'='*20} 股票: {ticker} 处理完成 {'='*20}")

    print("\n" + "=" * 50)
    print(f"所有股票处理完成。结果汇总目录: {base_run_dir}")
    print(f"总体结果摘要 ({len(all_results_summary)} 个股票):")
    for summary_item in all_results_summary:
        ticker_name = summary_item.get('ticker', 'UnknownTicker')
        status = summary_item.get('status', 'unknown')
        if status in ["data_preparation_critical_error", "data_preparation_failed", "run_error"]:
            print(f"  - Ticker: {ticker_name}, Status: {status.replace('_', ' ').title()}, Message: {summary_item.get('error_message', 'N/A')}")
        elif summary_item.get("final_state"):
            final_state = summary_item["final_state"]
            cash = final_state.get("cash", 0)
            position_values = sum(final_state.get("position_values", {}).values())
            net_worth = cash + position_values
            initial_net_worth = final_state.get("initial_net_worth", TRADING_ENV_CONFIG["starting_cash"])
            total_return = (net_worth / initial_net_worth - 1) * 100 if initial_net_worth > 0 else 0
            print(f"  - Ticker: {ticker_name}, Final Net Worth: ${net_worth:.2f}, Total Return: {total_return:.2f}%")
        else:
            # This case handles results that are not errors but also don't have 'final_state'
            # It might indicate a successful run that didn't produce expected final metrics or an incomplete result object.
            print(f"  - Ticker: {ticker_name}, Status: {status if status != 'unknown' else 'Completed (structure varies)'}")
    print("=" * 50)
    
    return 0

def adapt_langgraph_result(langgraph_result: Dict[str, Any], ticker: str, run_id: str) -> Dict[str, Any]:
    """
    Adapt LangGraph result format to maintain compatibility with existing reporting system
    
    Args:
        langgraph_result: Result from LangGraphCoordinator.run()
        ticker: Stock ticker
        run_id: Run identifier
        
    Returns:
        Adapted result in expected format
    """
    try:
        # Extract final state from LangGraph result
        final_state_obj = langgraph_result.get("final_state")
        
        if hasattr(final_state_obj, 'to_dict'):
            final_state = final_state_obj.to_dict()
        elif isinstance(final_state_obj, dict):
            final_state = final_state_obj
        else:
            final_state = {}
        
        # Calculate portfolio metrics
        cash = final_state.get("cash", 100000.0)
        positions = final_state.get("positions", {})
        
        # Calculate position values (simplified - would need current prices)
        position_values = {}
        total_position_value = 0.0
        
        if positions and final_state.get("ohlcv_data"):
            ohlcv_data = final_state["ohlcv_data"]
            for pos_ticker, quantity in positions.items():
                if pos_ticker in ohlcv_data and ohlcv_data[pos_ticker]:
                    # Get latest price (simplified)
                    ticker_data = ohlcv_data[pos_ticker]
                    if isinstance(ticker_data, dict) and 'data' in ticker_data:
                        # Handle serialized DataFrame format
                        data_records = ticker_data['data']
                        if data_records:
                            latest_close = data_records[-1].get('Close', 0)
                            position_value = quantity * latest_close
                            position_values[pos_ticker] = position_value
                            total_position_value += position_value
        
        net_worth = cash + total_position_value
        initial_net_worth = TRADING_ENV_CONFIG["starting_cash"]
        total_return = (net_worth / initial_net_worth - 1) * 100 if initial_net_worth > 0 else 0
        
        # Build compatible result structure
        adapted_result = {
            "ticker": ticker,
            "run_id": run_id,
            "status": "completed",
            "execution_time": langgraph_result.get("execution_time", 0),
            "final_state": {
                "cash": cash,
                "positions": positions,
                "position_values": position_values,
                "net_worth": net_worth,
                "initial_net_worth": initial_net_worth,
                "total_return": total_return
            },
            "agent_outputs": {
                "naa_output": final_state.get("naa_output", {}),
                "taa_output": final_state.get("taa_output", {}),
                "faa_output": final_state.get("faa_output", {}),
                "boa_output": final_state.get("boa_output", {}),
                "beoa_output": final_state.get("beoa_output", {}),
                "noa_output": final_state.get("noa_output", {}),
                "tra_output": final_state.get("tra_output", {})
            },
            "trading_history": final_state.get("trading_history", []),
            "execution_stats": langgraph_result.get("execution_stats", {}),
            "langgraph_metadata": {
                "step_count": final_state.get("step_count", 0),
                "myerson_values": final_state.get("myerson_values", {}),
                "agent_rewards": final_state.get("agent_rewards", {})
            }
        }
        
        # Add daily returns if available (simplified)
        if final_state.get("trading_history"):
            adapted_result["daily_returns"] = calculate_daily_returns_from_history(
                final_state["trading_history"], 
                initial_net_worth
            )
        
        return adapted_result
        
    except Exception as e:
        print(f"❌ Error adapting LangGraph result: {e}")
        # Return minimal result structure on error
        return {
            "ticker": ticker,
            "run_id": run_id,
            "status": "adaptation_error",
            "error_message": str(e),
            "raw_langgraph_result": langgraph_result
        }

def calculate_daily_returns_from_history(trading_history: List[Dict[str, Any]], 
                                       initial_value: float) -> Dict[str, Dict[str, float]]:
    """
    Calculate daily returns from trading history (simplified implementation)
    
    Args:
        trading_history: List of trading records
        initial_value: Initial portfolio value
        
    Returns:
        Dictionary of daily returns
    """
    daily_returns = {}
    
    # Group trades by date
    trades_by_date = {}
    for trade in trading_history:
        date = trade.get("timestamp", "")
        if date:
            if date not in trades_by_date:
                trades_by_date[date] = []
            trades_by_date[date].append(trade)
    
    # Calculate returns for each date (simplified)
    for date, trades in trades_by_date.items():
        total_trade_value = sum(trade.get("total_value", 0) for trade in trades)
        # Simplified return calculation
        portfolio_return = total_trade_value / initial_value if initial_value > 0 else 0
        benchmark_return = 0.001  # Simplified benchmark
        
        daily_returns[date] = {
            "portfolio_return": portfolio_return,
            "benchmark_return": benchmark_return
        }
    
    return daily_returns

def prepare_base_config(args, base_run_id: str) -> Dict[str, Any]:
    """
    准备基础配置 (通用部分，不包含特定于单个 ticker 的 env_config['stocks'])
    """
    env_config_base = {
        "start_date": args.start_date,
        "end_date": args.end_date,
        "starting_cash": args.starting_cash,
        "trading_fee_rate": TRADING_ENV_CONFIG["trading_fee_rate"],
        "price_window": TRADING_ENV_CONFIG["price_window"],
        "news_window": TRADING_ENV_CONFIG["news_window"],
        "fundamental_window": TRADING_ENV_CONFIG["fundamental_window"]
    }
    
    model_config = MODEL_CONFIG.copy()
    model_config["model_name"] = args.model
    
    optimization_config = {
        "enabled": args.optimize,
        "optimizer_type": args.optimizer,
        "exploration_rate": args.exploration_rate,
        "max_optimization_steps": args.max_optimization_steps,
        "history_prompts_k": args.history_prompts_k if args.optimizer != "opro" else 8,  # OPRO使用更多历史
        "initial_max_edit_words": args.initial_max_edit_words,
        "plateau_patience": args.plateau_patience,
        "max_num_generated_instructions": args.max_num_generated_instructions,  # OPRO特有参数
        "load_best_params": args.load_best_params
    }
    
    reflection_config = {"enabled": not args.no_reflection}
    
    # 添加 Myerson-POMDP 配置
    myerson_config = {
        "enabled": args.use_myerson,
        "use_llm_characteristic": args.use_llm_characteristic,
        "cache_dir": args.myerson_cache_dir if args.myerson_cache_dir else f"logs/llm_cache/{base_run_id}",
        "use_myerson_pomdp_integration": args.myerson_pomdp_integration
    }
    
    config = {
        "base_run_id": base_run_id, 
        "requested_tickers": args.tickers, 
        "timestamp": datetime.now().isoformat(),
        "env_config_base": env_config_base, 
        "model_config": model_config,
        "optimization_config": optimization_config,
        "reflection_config": reflection_config,
        "myerson_config": myerson_config,  # 添加 Myerson 配置
        "verbose": args.verbose,
        "days_to_run": args.days,
        "plot_charts": args.plot
    }
    return config

def prepare_ticker_specific_config(base_config: Dict[str, Any], ticker: str, ticker_specific_identifier: str) -> Dict[str, Any]:
    """
    为特定 Ticker 准备配置 (适配 LangGraphCoordinator)
    """
    # Extract optimization and myerson settings
    optimization_config = base_config["optimization_config"]
    myerson_config = base_config["myerson_config"]
    
    ticker_cfg = {
        "run_id": ticker_specific_identifier, 
        "timestamp": base_config["timestamp"],
        "model_config": base_config["model_config"].copy(),
        "optimization_config": optimization_config.copy(),
        "reflection_config": base_config["reflection_config"].copy(),
        "myerson_config": myerson_config.copy(),
        "verbose": base_config["verbose"],
        "env_config": base_config["env_config_base"].copy(),
        
        # LangGraphCoordinator specific configuration
        "enable_optimization": optimization_config.get("enabled", False),
        "enable_myerson_calculation": myerson_config.get("enabled", False),
        "optimizer_type": optimization_config.get("optimizer_type", "myerson_opro"),
        "max_optimization_steps": optimization_config.get("max_optimization_steps", 15),
        "history_prompts_k": optimization_config.get("history_prompts_k", 6),
        "max_num_generated_instructions": optimization_config.get("max_num_generated_instructions", 3),
        "plateau_patience": optimization_config.get("plateau_patience", 4),
        "exploration_rate": optimization_config.get("exploration_rate", 0.1),
        "load_best_params": optimization_config.get("load_best_params", False),
        
        # Myerson specific settings
        "use_llm_characteristic": myerson_config.get("use_llm_characteristic", False),
        "myerson_cache_dir": myerson_config.get("cache_dir", ""),
        "use_myerson_pomdp_integration": myerson_config.get("use_myerson_pomdp_integration", False),
        
        # Model interface configuration
        "model_name": base_config["model_config"].get("model_name", DEFAULT_MODEL),
    }
    ticker_cfg["env_config"]["stocks"] = [ticker] 
    
    return ticker_cfg

def plot_returns(result: Dict[str, Any], ticker_run_dir: str, ticker: str) -> None:
    """
    绘制收益率曲线
    """
    try:
        daily_returns = result.get('daily_returns', {})
        if not daily_returns:
            print(f"警告 ({ticker}): 没有可用的每日收益数据，无法绘制曲线", file=sys.stderr)
            return
        
        try:
            dates = sorted(daily_returns.keys(), key=lambda d: datetime.strptime(d, '%Y-%m-%d'))
        except ValueError: 
            dates = sorted(daily_returns.keys())
            print(f"警告 ({ticker}): 日期键未使用标准 YYYY-MM-DD 格式，按字符串排序。", file=sys.stderr)

        portfolio_returns = [daily_returns[date].get('portfolio_return', 0) for date in dates]
        benchmark_returns = [daily_returns[date].get('benchmark_return', 0) for date in dates]
        
        portfolio_cumulative = [1.0] 
        benchmark_cumulative = [1.0] 
        
        for r in portfolio_returns:
            portfolio_cumulative.append(portfolio_cumulative[-1] * (1 + r))
        for r in benchmark_returns:
            benchmark_cumulative.append(benchmark_cumulative[-1] * (1 + r))
        
        portfolio_cumulative_percent = [(v - 1) * 100 for v in portfolio_cumulative[1:]]
        benchmark_cumulative_percent = [(v - 1) * 100 for v in benchmark_cumulative[1:]]
        
        if not dates or not portfolio_cumulative_percent: 
            print(f"注意 ({ticker}): 累积收益数据为空或日期列表为空，无法绘制曲线", file=sys.stderr)
            return

        plt.figure(figsize=(12, 6))
        plt.plot(dates, portfolio_cumulative_percent, label=f'投资组合 ({ticker})', color='blue', linewidth=2)
        plt.plot(dates, benchmark_cumulative_percent, label=f'基准(Buy & Hold {ticker})', color='red', linestyle='--')
        
        plt.title(f'{ticker} - 多智能体交易系统收益率曲线 vs 买入持有策略')
        plt.xlabel('日期')
        plt.ylabel('累积收益率 (%)')
        plt.legend()
        plt.grid(True, linestyle='--', alpha=0.7)
        
        num_dates = len(dates)
        if num_dates > 20: 
            tick_step = max(1, num_dates // 10) 
            plt.xticks(dates[::tick_step], rotation=45)
        elif num_dates > 0 :
            plt.xticks(rotation=45)
        
        plt.tight_layout()
        
        chart_path = os.path.join(ticker_run_dir, f'returns_chart_{ticker}.png')
        plt.savefig(chart_path)
        print(f"股票 {ticker} 的收益率曲线已保存到: {chart_path}")
        plt.close()
        
    except Exception as e:
        print(f"绘制股票 {ticker} 的收益率曲线出错: {str(e)}", file=sys.stderr)
        import traceback
        traceback.print_exc(file=sys.stderr)

def print_summary(result: Dict[str, Any], ticker: str, result_path: str) -> None:
    """
    打印运行结果摘要
    result_path is the path where this ticker's detailed result was saved.
    """
    print("\n" + "-" * 50)
    print(f"股票 {ticker} 运行完成。") 
    
    days_run = result.get('days')
    if days_run is not None:
        print(f"运行天数: {days_run}")

    run_time = result.get('run_time')
    if run_time is not None:
        print(f"运行时间: {run_time:.2f} 秒")
    
    if result.get("final_state"):
        final_state = result["final_state"]
        cash = final_state.get("cash", 0)
        position_values = sum(final_state.get("position_values", {}).values())
        net_worth = cash + position_values
        initial_net_worth = final_state.get("initial_net_worth", TRADING_ENV_CONFIG["starting_cash"])
        total_return = ((net_worth / initial_net_worth) - 1) * 100 if initial_net_worth > 0 else 0
        
        print("-" * 20 + f" {ticker} 财务摘要 " + "-" * 20)
        print(f"初始资产净值: ${initial_net_worth:.2f}")
        print(f"最终资产净值: ${net_worth:.2f}")
        print(f"  现金: ${cash:.2f}")
        print(f"  持仓价值: ${position_values:.2f}")
        print(f"总收益率: {total_return:.2f}%")
        
        if 'daily_returns' in result and result['daily_returns']:
            try:
                sorted_dates = sorted(result['daily_returns'].keys(), key=lambda d: datetime.strptime(d, '%Y-%m-%d'))
            except ValueError:
                sorted_dates = sorted(result['daily_returns'].keys())
                print(f"警告 ({ticker}): 摘要中每日收益日期键未使用标准 YYYY-MM-DD 格式，按字符串排序。", file=sys.stderr)

            benchmark_values = [1.0]
            for date_key in sorted_dates:
                returns = result['daily_returns'][date_key]
                benchmark_values.append(benchmark_values[-1] * (1 + returns.get('benchmark_return', 0)))
            
            if len(benchmark_values) > 1:
                 benchmark_return_calc = (benchmark_values[-1] - 1) * 100
                 print(f"基准策略(买入持有)收益率: {benchmark_return_calc:.2f}%")
                 print(f"策略超额收益: {total_return - benchmark_return_calc:.2f}%")
            else:
                print("基准策略(买入持有)收益率: N/A (无足够数据)")
                
        # 打印 Myerson 值摘要（如果存在）
        if 'myerson_values' in result:
            print("-" * 20 + " Myerson 值摘要 " + "-" * 20)
            for agent_id, value in result.get('myerson_values', {}).items():
                print(f"  {agent_id}: {value:.4f}")
        
        print("-" * 50)

if __name__ == "__main__":
    sys.exit(main()) 